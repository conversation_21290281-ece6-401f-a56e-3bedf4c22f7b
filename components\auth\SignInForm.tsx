'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const SignInForm = () => {
  const router = useRouter();

  useEffect(() => {
    // Redirect to OTP login page
    router.push('/login-with-otp');
  }, [router]);

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Redirecting...</h2>
      <p className="text-center text-gray-600">
        Please wait while we redirect you to the secure login page.
      </p>
    </div>
  );
};

export default SignInForm;
