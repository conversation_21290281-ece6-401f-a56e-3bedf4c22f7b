'use client';

import { useState, useEffect } from 'react';
import { User } from '@/types';
import { PLAN_PRICES } from '@/utils/cashfreeConfig';
import Script from 'next/script';
import { CASHFREE_JS_URL } from '@/utils/cashfreeConfig';

interface SubscriptionSectionProps {
  user: User;
}

interface SubscriptionDetails {
  plan: string;
  status: string;
  billingCycle: string;
  expiryDate: string | null;
  daysLeft: number | null;
}

declare global {
  interface Window {
    Cashfree: (config: { mode: 'sandbox' | 'production' }) => {
      checkout: (options: {
        paymentSessionId: string;
        redirectTarget?: string;
      }) => Promise<{
        error?: any;
        redirect?: boolean;
        paymentDetails?: {
          paymentMessage: string;
          [key: string]: any;
        };
      }>;
    };
  }
}

const SubscriptionSection = ({ user }: SubscriptionSectionProps) => {
  const [subscriptionDetails, setSubscriptionDetails] = useState<SubscriptionDetails>({
    plan: user.plan,
    status: user.paymentStatus || 'pending',
    billingCycle: 'monthly',
    expiryDate: null,
    daysLeft: null,
  });

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRenewing, setIsRenewing] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(user.plan);
  const [selectedBillingCycle, setSelectedBillingCycle] = useState('monthly');
  const [orderToken, setOrderToken] = useState<string | null>(null);

  // Fetch subscription details
  useEffect(() => {
    const fetchSubscriptionDetails = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/user/profile?userId=${user.id}`);
        const data = await response.json();

        if (data.success && data.user) {
          // Calculate days left if expiry date exists
          let daysLeft = null;
          if (data.user.paymentExpiryDate) {
            const expiryDate = new Date(data.user.paymentExpiryDate);
            const today = new Date();
            const diffTime = expiryDate.getTime() - today.getTime();
            daysLeft = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          }

          setSubscriptionDetails({
            plan: data.user.plan,
            status: data.user.paymentStatus || 'pending',
            billingCycle: data.user.paymentBillingCycle || 'monthly',
            expiryDate: data.user.paymentExpiryDate,
            daysLeft: daysLeft,
          });

          setSelectedPlan(data.user.plan);
          setSelectedBillingCycle(data.user.paymentBillingCycle || 'monthly');
        }
      } catch (error) {
        console.error('Error fetching subscription details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscriptionDetails();
  }, [user.id]);

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Get plan price
  const getPlanPrice = (plan: string, billingCycle: string) => {
    if (plan === 'free') return 0;

    if (plan === 'standard') {
      return billingCycle === 'monthly'
        ? PLAN_PRICES.standard.monthly
        : PLAN_PRICES.standard.annual / 12;
    }

    if (plan === 'pro') {
      return billingCycle === 'monthly'
        ? PLAN_PRICES.pro.monthly
        : PLAN_PRICES.pro.annual / 12;
    }

    return 0;
  };

  // Get total price
  const getTotalPrice = (plan: string, billingCycle: string) => {
    if (plan === 'free') return 0;

    if (plan === 'standard') {
      return billingCycle === 'monthly'
        ? PLAN_PRICES.standard.monthly
        : PLAN_PRICES.standard.annual;
    }

    if (plan === 'pro') {
      return billingCycle === 'monthly'
        ? PLAN_PRICES.pro.monthly
        : PLAN_PRICES.pro.annual;
    }

    return 0;
  };

  // Handle plan change
  const handlePlanChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedPlan(e.target.value as 'free' | 'standard' | 'pro');
  };

  // Handle billing cycle change
  const handleBillingCycleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedBillingCycle(e.target.value as 'monthly' | 'annual');
  };

  // Handle renew subscription
  const handleRenewSubscription = async () => {
    try {
      setIsRenewing(true);
      setError(null);

      // Skip if free plan
      if (selectedPlan === 'free') {
        setError('Free plan does not require renewal');
        setIsRenewing(false);
        return;
      }

      // Create renewal order
      const response = await fetch('/api/user/renew-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          plan: selectedPlan,
          billingCycle: selectedBillingCycle,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setOrderToken(data.orderToken);

        // Initialize Cashfree checkout
        if (typeof window !== 'undefined' && typeof window.Cashfree === 'function') {
          const cashfree = window.Cashfree({
            mode: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
          });

          cashfree.checkout({
            paymentSessionId: data.orderToken,
            redirectTarget: '_self',
          }).then(function(result) {
            if (result.error) {
              console.error('Cashfree checkout error:', result.error);
              setError('Payment failed: ' + (result.error.message || 'Unknown error'));
            }

            if (result.redirect) {
              console.log('Redirecting to payment gateway...');
            }
          }).catch(function(error) {
            console.error('Cashfree checkout exception:', error);
            setError('Payment initialization failed');
          });
        } else {
          setError('Payment gateway not available. Please try again later.');
        }
      } else {
        setError(data.message || 'Failed to create renewal order');
      }
    } catch (error) {
      setError('An error occurred while processing your renewal');
      console.error('Renewal error:', error);
    } finally {
      setIsRenewing(false);
    }
  };

  return (
    <div>
      <Script
        src={CASHFREE_JS_URL}
        strategy="afterInteractive"
        onError={() => {
          console.error('Failed to load Cashfree script');
          setError('Failed to load payment gateway. Please refresh the page and try again.');
        }}
      />

      <h2 className="text-2xl font-bold text-gray-900 mb-6">Subscription Details</h2>

      {error && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Current Subscription */}
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Current Subscription</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Plan</p>
                <p className="font-medium text-gray-900">
                  {subscriptionDetails.plan.charAt(0).toUpperCase() + subscriptionDetails.plan.slice(1)}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Status</p>
                <p className="font-medium text-gray-900">
                  {subscriptionDetails.status.charAt(0).toUpperCase() + subscriptionDetails.status.slice(1)}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Billing Cycle</p>
                <p className="font-medium text-gray-900">
                  {subscriptionDetails.billingCycle.charAt(0).toUpperCase() + subscriptionDetails.billingCycle.slice(1)}
                </p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Expiry Date</p>
                <p className="font-medium text-gray-900">
                  {formatDate(subscriptionDetails.expiryDate)}
                </p>
              </div>

              {subscriptionDetails.daysLeft !== null && (
                <div className="col-span-1 md:col-span-2">
                  <p className="text-sm text-gray-500">Days Left</p>
                  <p className={`font-medium ${
                    subscriptionDetails.daysLeft < 7 ? 'text-red-600' : 'text-gray-900'
                  }`}>
                    {subscriptionDetails.daysLeft} days
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Renew Subscription */}
          <div className="border border-gray-200 p-6 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Renew Subscription</h3>

            <div className="space-y-4">
              <div>
                <label htmlFor="plan" className="block text-sm font-medium text-gray-700">
                  Select Plan
                </label>
                <select
                  id="plan"
                  name="plan"
                  value={selectedPlan}
                  onChange={handlePlanChange}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                >
                  <option value="standard">Standard</option>
                  <option value="pro">Pro</option>
                </select>
              </div>

              <div>
                <label htmlFor="billingCycle" className="block text-sm font-medium text-gray-700">
                  Billing Cycle
                </label>
                <select
                  id="billingCycle"
                  name="billingCycle"
                  value={selectedBillingCycle}
                  onChange={handleBillingCycleChange}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                >
                  <option value="monthly">Monthly</option>
                  <option value="annual">Annual (Save up to 25%)</option>
                </select>
              </div>

              <div className="bg-gray-50 p-4 rounded-md">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Price</span>
                  <span className="text-sm font-medium text-gray-900">
                    ₹{getPlanPrice(selectedPlan, selectedBillingCycle)}/month
                  </span>
                </div>

                {selectedBillingCycle === 'annual' && (
                  <div className="flex justify-between mt-2">
                    <span className="text-sm text-gray-500">Billed annually</span>
                    <span className="text-sm font-medium text-gray-900">
                      ₹{getTotalPrice(selectedPlan, selectedBillingCycle)}
                    </span>
                  </div>
                )}
              </div>

              <div className="pt-4">
                <button
                  type="button"
                  onClick={handleRenewSubscription}
                  disabled={isRenewing}
                  className="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  {isRenewing ? 'Processing...' : 'Renew Subscription'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionSection;
