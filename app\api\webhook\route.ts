import { NextRequest, NextResponse } from 'next/server';
import { WebhookData } from '@/types';

export const dynamic = 'force-dynamic';

// In-memory storage for demo purposes
// In a real application, you would use a database
let webhookData: WebhookData | null = null;

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Validate the incoming data
    if (!data.mainKeyword) {
      return NextResponse.json(
        { error: 'Main keyword is required' },
        { status: 400 }
      );
    }

    // Store the data
    webhookData = {
      mainKeyword: data.mainKeyword,
      location: data.location || 'United States',
      language: data.language || 'English',
      limit: data.limit || 10,
      date: data.date || new Date().toISOString().split('T')[0],
      timestamp: data.timestamp || new Date().toISOString(),
      keywordResearch: data.keywordResearch || [],
      contentMatrix: data.contentMatrix || [],
    };

    return NextResponse.json({ success: true, message: 'Data received successfully' });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Failed to process webhook data' },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Return the stored data or a default response
  if (!webhookData) {
    return NextResponse.json({ data: null });
  }

  return NextResponse.json({ data: webhookData });
}
