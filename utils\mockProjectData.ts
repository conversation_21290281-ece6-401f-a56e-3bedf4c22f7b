import { Project, Matrix } from '@/types';

// Mock projects data
export const getMockProjects = (userId: string): Project[] => {
  return [
    {
      id: '1',
      name: 'My Tech Blog',
      description: 'A blog about the latest technology trends and reviews',
      userId,
      website: 'https://techblog.example.com',
      niche: 'Technology',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'Fitness Website',
      description: 'Website focused on fitness tips and workout routines',
      userId,
      website: 'https://fitness.example.com',
      niche: 'Health & Fitness',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
  ];
};

// Get a project by ID
export const getMockProjectById = (userId: string, projectId: string): Project | undefined => {
  return getMockProjects(userId).find(p => p.id === projectId);
};

// Get a project by slug (name)
export const getMockProjectBySlug = (userId: string, slug: string): Project | undefined => {
  return getMockProjects(userId).find(p => 
    p.name.toLowerCase().replace(/\s+/g, '-') === slug
  );
};

// Get matrices for a project
export const getMockMatricesForProject = (userId: string, projectId: string): Matrix[] => {
  if (projectId === '1') {
    return [
      {
        id: '101',
        projectId,
        userId,
        mainKeyword: 'artificial intelligence',
        filename: 'artificial-intelligence-matrix.json',
        location: 'United States',
        language: 'English',
        keywordResearch: [],
        contentMatrix: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '102',
        projectId,
        userId,
        mainKeyword: 'machine learning',
        filename: 'machine-learning-matrix.json',
        location: 'United States',
        language: 'English',
        keywordResearch: [],
        contentMatrix: [],
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      }
    ];
  }
  return [];
};
