import React from 'react';
import { KeywordResearchItem, ContentMatrixItem } from '@/types';

interface ResultSummaryDashboardProps {
  keywordData: KeywordResearchItem[];
  contentData: ContentMatrixItem[];
  mainKeyword: string;
  location: string;
  language: string;
}

const ResultSummaryDashboard: React.FC<ResultSummaryDashboardProps> = ({
  keywordData,
  contentData,
  mainKeyword,
  location,
  language,
}) => {
  // Calculate summary statistics
  const totalKeywords = keywordData.length;
  const totalContentPieces = contentData.length;
  const totalSearchVolume = keywordData.reduce((sum, item) => sum + item.msv, 0);
  const avgSearchVolume = totalKeywords > 0 ? Math.round(totalSearchVolume / totalKeywords) : 0;
  
  // Keyword difficulty distribution
  const difficultyStats = keywordData.reduce((acc, item) => {
    acc[item.kwDifficulty] = (acc[item.kwDifficulty] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Search intent distribution
  const intentStats = keywordData.reduce((acc, item) => {
    acc[item.searchIntent] = (acc[item.searchIntent] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Content status distribution
  const statusStats = contentData.reduce((acc, item) => {
    acc[item.status] = (acc[item.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Content type distribution
  const typeStats = contentData.reduce((acc, item) => {
    acc[item.contentType] = (acc[item.contentType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // High opportunity keywords (low competition, decent volume)
  const highOpportunityKeywords = keywordData.filter(
    item => item.kwDifficulty === 'LOW' && item.msv > 20
  ).length;

  // Average CPC
  const avgCPC = keywordData.length > 0 
    ? (keywordData.reduce((sum, item) => sum + item.cpc, 0) / keywordData.length).toFixed(2)
    : '0.00';

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Content Matrix Results</h2>
        <div className="text-sm text-gray-600">
          <span className="font-medium">Keyword:</span> {mainKeyword} | 
          <span className="font-medium ml-2">Location:</span> {location} | 
          <span className="font-medium ml-2">Language:</span> {language}
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <div className="text-2xl font-bold text-blue-600">{totalKeywords}</div>
          <div className="text-sm text-blue-800">Total Keywords</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <div className="text-2xl font-bold text-green-600">{totalContentPieces}</div>
          <div className="text-sm text-green-800">Content Pieces</div>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
          <div className="text-2xl font-bold text-purple-600">{totalSearchVolume.toLocaleString()}</div>
          <div className="text-sm text-purple-800">Total Search Volume</div>
        </div>
        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
          <div className="text-2xl font-bold text-orange-600">{highOpportunityKeywords}</div>
          <div className="text-sm text-orange-800">High Opportunity</div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Keyword Insights</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Average Search Volume:</span>
              <span className="font-medium">{avgSearchVolume.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Average CPC:</span>
              <span className="font-medium">${avgCPC}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Keywords with Answers:</span>
              <span className="font-medium">{keywordData.filter(k => k.answer).length}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Content Overview</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Published Content:</span>
              <span className="font-medium text-green-600">{statusStats['Published'] || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Draft Content:</span>
              <span className="font-medium text-yellow-600">{statusStats['Draft'] || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Planned Content:</span>
              <span className="font-medium text-blue-600">{statusStats['Planned'] || 0}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Distribution Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Keyword Difficulty Distribution */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Keyword Difficulty</h3>
          <div className="space-y-2">
            {Object.entries(difficultyStats).map(([difficulty, count]) => (
              <div key={difficulty} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-2 ${
                    difficulty === 'LOW' ? 'bg-green-500' :
                    difficulty === 'MEDIUM' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-sm text-gray-600">{difficulty}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">{count}</span>
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        difficulty === 'LOW' ? 'bg-green-500' :
                        difficulty === 'MEDIUM' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${(count / totalKeywords) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Search Intent Distribution */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Search Intent</h3>
          <div className="space-y-2">
            {Object.entries(intentStats).map(([intent, count]) => (
              <div key={intent} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-2 ${
                    intent === 'transactional' ? 'bg-blue-500' :
                    intent === 'commercial' ? 'bg-purple-500' : 'bg-indigo-500'
                  }`}></div>
                  <span className="text-sm text-gray-600 capitalize">{intent}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">{count}</span>
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        intent === 'transactional' ? 'bg-blue-500' :
                        intent === 'commercial' ? 'bg-purple-500' : 'bg-indigo-500'
                      }`}
                      style={{ width: `${(count / totalKeywords) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Content Type Distribution */}
      <div className="mt-6 bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Content Types</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(typeStats).map(([type, count]) => (
            <div key={type} className="text-center">
              <div className="text-xl font-bold text-gray-900">{count}</div>
              <div className="text-xs text-gray-600">{type}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex flex-wrap gap-3">
          <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
            Export to CSV
          </button>
          <button className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors">
            Generate PDF Report
          </button>
          <button className="px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 transition-colors">
            Export to Excel
          </button>
          <button className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors">
            Save Matrix
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResultSummaryDashboard;
