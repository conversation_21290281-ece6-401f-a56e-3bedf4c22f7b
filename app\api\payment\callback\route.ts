import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { verifyCashfreeSignature } from '@/utils/cashfreeUtils';
import { verifyPaymentStatus } from '@/utils/cashfreeSdk';
import {
  logPaymentEvent,
  PAYMENT_STATUS,
  DB_PAYMENT_STATUS,
  ERROR_MESSAGES
} from '@/utils/cashfreeConfig';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    logPaymentEvent('PAYMENT_CALLBACK_RECEIVED', { url: request.url });

    // Get query parameters from the URL
    const searchParams = request.nextUrl.searchParams;
    const orderId = searchParams.get('orderId');
    const orderAmount = searchParams.get('orderAmount');
    const referenceId = searchParams.get('referenceId');
    const txStatus = searchParams.get('txStatus');
    const paymentMode = searchParams.get('paymentMode');
    const txMsg = searchParams.get('txMsg');
    const txTime = searchParams.get('txTime');
    const signature = searchParams.get('signature');

    // Log the callback parameters
    logPaymentEvent('PAYMENT_CALLBACK_PARAMS', {
      orderId,
      txStatus,
      referenceId: referenceId ? referenceId.substring(0, 8) + '...' : 'null',
      paymentMode
    });

    // Validate input - we need at least the orderId
    if (!orderId) {
      logPaymentEvent('PAYMENT_CALLBACK_INVALID_PARAMS', { orderId: 'missing' });
      return new Response('Invalid payment data: Missing order ID', { status: 400 });
    }

    // If txStatus or signature is missing, we'll try to verify the payment status directly with Cashfree API
    if (!txStatus || !signature) {
      logPaymentEvent('PAYMENT_CALLBACK_INCOMPLETE_PARAMS', {
        orderId,
        hasTxStatus: !!txStatus,
        hasSignature: !!signature
      });
    }

    // Only verify signature if we have all required parameters
    let isValidSignature = false;

    if (txStatus && signature) {
      isValidSignature = verifyCashfreeSignature(
        orderId,
        orderAmount || '',
        referenceId || '',
        txStatus,
        paymentMode || '',
        txMsg || '',
        txTime || '',
        signature
      );

      if (!isValidSignature) {
        logPaymentEvent('PAYMENT_CALLBACK_INVALID_SIGNATURE', { orderId });
        // Don't return error immediately, try to verify with API first
      }
    }

    // Connect to database
    await dbConnect();

    // Find the user with this order ID
    const user = await User.findOne({ paymentOrderId: orderId });

    if (!user) {
      logPaymentEvent('PAYMENT_CALLBACK_USER_NOT_FOUND', { orderId });
      return new Response(ERROR_MESSAGES.USER_NOT_FOUND, { status: 404 });
    }

    // ALWAYS verify the payment status with Cashfree API
    let verifiedPaymentStatus = null;
    let verifiedReferenceId = referenceId;
    let orderIsPaid = false;

    try {
      // Always get the order details from Cashfree API to verify the order status
      const { getOrderDetails } = await import('@/utils/cashfreeSdk');
      const orderDetails = await getOrderDetails(orderId);

      if (orderDetails) {
        // Check if the order status is PAID - this is the most reliable indicator
        orderIsPaid = orderDetails.order_status === 'PAID';
        verifiedPaymentStatus = orderDetails.order_status;

        logPaymentEvent('ORDER_DETAILS_FETCHED', {
          orderId,
          orderStatus: verifiedPaymentStatus,
          isPaid: orderIsPaid
        });

        // If there's a payment, get the reference ID
        if (orderDetails.payments && orderDetails.payments.length > 0) {
          verifiedReferenceId = orderDetails.payments[0].cf_payment_id || orderDetails.payments[0].payment_id;

          logPaymentEvent('PAYMENT_DETAILS_FETCHED', {
            orderId,
            referenceId: verifiedReferenceId,
            paymentStatus: orderDetails.payments[0].payment_status
          });
        }
      } else {
        logPaymentEvent('ORDER_DETAILS_NOT_FOUND', { orderId });
      }

      // If we have a referenceId, also verify the specific payment
      if (referenceId && !orderIsPaid) {
        try {
          const paymentVerification = await verifyPaymentStatus(orderId, referenceId);

          if (paymentVerification.success) {
            // If payment verification is successful, consider it as a backup confirmation
            orderIsPaid = true;
            verifiedPaymentStatus = 'SUCCESS';

            logPaymentEvent('PAYMENT_VERIFICATION_SUCCESS', {
              orderId,
              callbackStatus: txStatus || 'UNKNOWN',
              apiStatus: paymentVerification.status
            });
          } else {
            logPaymentEvent('PAYMENT_VERIFICATION_MISMATCH', {
              orderId,
              callbackStatus: txStatus || 'UNKNOWN',
              apiStatus: paymentVerification.status
            });
          }
        } catch (paymentVerifyError) {
          logPaymentEvent('PAYMENT_VERIFICATION_ERROR', {
            orderId,
            error: paymentVerifyError instanceof Error ? paymentVerifyError.message : 'Unknown error'
          });
        }
      }
    } catch (verifyError) {
      // Log the error but continue with the callback parameters
      logPaymentEvent('ORDER_VERIFICATION_ERROR', {
        orderId,
        error: verifyError instanceof Error ? verifyError.message : 'Unknown error'
      });
    }

    // Determine the payment status from callback or API verification
    const finalTxStatus = verifiedPaymentStatus || txStatus;
    const finalReferenceId = verifiedReferenceId || referenceId;

    // Update user's payment status based on order verification
    // ONLY consider the payment successful if the order status is PAID or we have explicit confirmation
    if (orderIsPaid ||
        finalTxStatus === 'PAID' ||
        (isValidSignature && finalTxStatus === PAYMENT_STATUS.SUCCESS)) {
      user.paymentStatus = DB_PAYMENT_STATUS.COMPLETED;
      user.paymentReference = finalReferenceId;
      user.paymentMode = paymentMode || 'unknown';
      user.paymentTime = new Date(txTime || Date.now());
      await user.save();

      logPaymentEvent('PAYMENT_SUCCESS_DB_UPDATED', {
        orderId,
        userId: user._id,
        plan: user.paymentPlan || user.plan,
        status: finalTxStatus,
        source: verifiedPaymentStatus ? 'API_VERIFICATION' : 'CALLBACK_PARAMS'
      });

      // Add orderId to the success URL for verification on the success page
      return NextResponse.redirect(new URL(`/payment/success?orderId=${orderId}`, request.url));
    } else {
      user.paymentStatus = DB_PAYMENT_STATUS.FAILED;
      user.paymentReference = finalReferenceId;
      user.paymentFailureReason = txMsg || `Payment failed or cancelled (${finalTxStatus || 'unknown status'})`;
      await user.save();

      logPaymentEvent('PAYMENT_FAILURE_DB_UPDATED', {
        orderId,
        userId: user._id,
        status: finalTxStatus || 'UNKNOWN',
        message: txMsg || 'No message provided',
        source: verifiedPaymentStatus ? 'API_VERIFICATION' : 'CALLBACK_PARAMS'
      });

      // Redirect to failure page
      return NextResponse.redirect(new URL('/payment/failure', request.url));
    }
  } catch (error) {
    logPaymentEvent('PAYMENT_CALLBACK_ERROR', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return new Response('An error occurred while processing payment callback', { status: 500 });
  }
}
