import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { createCashfreeOrder, generateOrderId, CashfreeOrderRequest } from '@/utils/cashfreeUtils';
import {
  PAYMENT_CURRENCY,
  getReturnUrl,
  WEBHOOK_URL,
  PAYMENT_MODES,
  logPaymentEvent,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  DB_PAYMENT_STATUS
} from '@/utils/cashfreeConfig';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    logPaymentEvent('CREATE_ORDER_API_REQUEST', { url: request.url });

    const body = await request.json();
    const { userId, plan, amount, billingCycle = 'monthly' } = body;

    // Validate input
    if (!userId || !plan) {
      return NextResponse.json(
        { success: false, message: 'User ID and plan are required' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      logPaymentEvent('USER_NOT_FOUND', { userId });
      return NextResponse.json(
        { success: false, message: ERROR_MESSAGES.USER_NOT_FOUND },
        { status: 404 }
      );
    }

    // Generate a unique order ID
    const orderId = generateOrderId(userId);

    // Create order data for Cashfree
    const orderData: CashfreeOrderRequest = {
      order_id: orderId,
      order_amount: amount,
      order_currency: PAYMENT_CURRENCY,
      order_note: `Payment for ${plan} plan (${billingCycle})`,
      customer_details: {
        customer_id: userId,
        customer_name: user.name,
        customer_email: user.email,
        customer_phone: user.phone || '9999999999' // Required by Cashfree
      },
      order_meta: {
        return_url: getReturnUrl(orderId),
        notify_url: WEBHOOK_URL || undefined,
        payment_methods: PAYMENT_MODES || undefined
      },
    };

    logPaymentEvent('CREATE_ORDER_DATA', {
      orderId,
      userId,
      plan,
      amount,
      billingCycle
    });

    // Create order with Cashfree
    const cashfreeOrder = await createCashfreeOrder(orderData);

    // Store order information in the user document
    user.paymentOrderId = orderId;
    user.paymentStatus = DB_PAYMENT_STATUS.PENDING;
    user.paymentPlan = plan;
    user.paymentAmount = amount;
    user.paymentBillingCycle = billingCycle;
    user.paymentCreatedAt = new Date();
    await user.save();

    logPaymentEvent('ORDER_CREATED_DB_UPDATED', {
      orderId,
      userId,
      paymentStatus: DB_PAYMENT_STATUS.PENDING
    });

    // Return success response with order token
    return NextResponse.json({
      success: true,
      message: SUCCESS_MESSAGES.ORDER_CREATED,
      orderId: cashfreeOrder.order_id,
      orderToken: cashfreeOrder.order_token || cashfreeOrder.payment_session_id,
      cfOrderId: cashfreeOrder.cf_order_id,
      orderStatus: cashfreeOrder.order_status,
      orderExpiry: cashfreeOrder.order_expiry_time
    });
  } catch (error) {
    // Log the error
    logPaymentEvent('CREATE_ORDER_API_ERROR', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    // Get detailed error message
    const errorMessage = error instanceof Error
      ? error.message
      : 'An unknown error occurred';

    return NextResponse.json(
      {
        success: false,
        message: ERROR_MESSAGES.ORDER_CREATION_FAILED,
        error: errorMessage
      },
      { status: 500 }
    );
  }
}
