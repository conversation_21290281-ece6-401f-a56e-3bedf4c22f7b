import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Project from '@/models/Project';
import { verifyToken } from '@/utils/jwtUtils';

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const userId = userData.id;
    const projects = await Project.find({ userId }).sort({ createdAt: -1 });

    return NextResponse.json({ projects });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const body = await req.json();
    const { name, description, website, niche } = body;

    if (!name || !niche) {
      return NextResponse.json({ error: 'Name and niche are required' }, { status: 400 });
    }

    const userId = userData.id;

    const project = await Project.create({
      name,
      description,
      website,
      niche,
      userId,
      keywordCount: 0,
      clusterCount: 0
    });

    return NextResponse.json({ project }, { status: 201 });
  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json({ error: 'Failed to create project' }, { status: 500 });
  }
}
