import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { verifyOTP } from '../otp-actions';
import { sendOTPVerificationToWebhook } from '@/utils/webhookUtils';
import OTP from '@/models/OTP';
import { generateToken } from '@/utils/jwtUtils';

export async function POST(request: NextRequest) {
  try {
    const { email, otp } = await request.json();

    // Validate input
    if (!email || !otp) {
      return NextResponse.json(
        { success: false, message: 'Email and <PERSON>TP are required' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Verify OTP
    const isValid = await verifyOTP(email, otp);

    if (!isValid) {
      return NextResponse.json(
        { success: false, message: 'Invalid or expired OTP' },
        { status: 400 }
      );
    }

    // Get user
    const user = await User.findOne({ email });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Check if this is a signup flow by looking for the 'signup' route parameter
    // that was passed when the OTP was created
    const isSignupFlow = request.headers.get('x-auth-flow') === 'signup';

    // Only send verification webhook for signup flows
    if (isSignupFlow) {
      try {
        // Get the user's plan
        const plan = user.plan;
        const webhookResponse = await sendOTPVerificationToWebhook(user.name, email, plan);

        // Check if the webhook response indicates successful sending
        const isSent = webhookResponse?.isSent === true ||
                       webhookResponse?.success === true ||
                       webhookResponse === true ||
                       webhookResponse === "true" ||
                       (typeof webhookResponse === 'string' && webhookResponse.toLowerCase() === "true") ||
                       (webhookResponse?.message === "Workflow was started") ||
                       (webhookResponse?.message === "OTP would be sent in production environment");

        if (isSent) {
          console.log(`OTP verification signal sent for signup: ${email} with plan: ${plan}`);
        } else {
          console.warn('Webhook response indicated verification signal was not sent:', JSON.stringify(webhookResponse));
        }
      } catch (webhookError) {
        // Log the error but don't fail the verification process
        console.error('Error sending OTP verification signal:', webhookError);
      }
    }

    // Generate JWT token
    const token = generateToken(user);

    // Return user data and token
    return NextResponse.json({
      success: true,
      message: 'OTP verified successfully',
      token, // Include the JWT token in the response
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        plan: user.plan,
        paymentStatus: user.paymentStatus,
        createdAt: user.createdAt,
      },
    });
  } catch (error) {
    console.error('Error verifying OTP:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to verify OTP' },
      { status: 500 }
    );
  }
}
