'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

const TestimonialsSection = () => {
  const testimonials = [
    {
      content:
        "The SEO Content Matrix Generator has completely transformed our content strategy. We've seen a 45% increase in organic traffic since implementing the insights from this tool.",
      author: '<PERSON>',
      role: 'Content Marketing Manager',
      company: 'TechGrowth',
    },
    {
      content:
        "I've tried many SEO tools, but this one stands out for its comprehensive content planning capabilities. It's like having an SEO expert on your team.",
      author: '<PERSON>',
      role: 'SEO Specialist',
      company: 'Digital Boost Agency',
    },
    {
      content:
        "The keyword clustering feature alone is worth the investment. We're now creating more targeted content that ranks for multiple related keywords.",
      author: '<PERSON>',
      role: 'Content Director',
      company: 'ContentPro',
    },
    {
      content:
        "This tool has saved our team countless hours of manual keyword research and organization. The content maps are incredibly intuitive and actionable.",
      author: '<PERSON>',
      role: 'Digital Marketing Lead',
      company: 'GrowthHackers',
    },
    {
      content:
        "The local topics feature has been a game-changer for our multi-location business. We can now create targeted content for each market with minimal effort.",
      author: '<PERSON>',
      role: 'Local SEO Strategist',
      company: 'LocalEdge Marketing',
    },
    {
      content:
        "As someone without technical SEO knowledge, I was able to create a comprehensive content strategy in just a few hours. The ROI has been incredible.",
      author: '<PERSON> <PERSON>',
      role: 'Small Business Owner',
      company: '<PERSON> Consulting',
    },
    {
      content:
        "The export feature makes it easy to share content plans with our writing team. Our content production efficiency has improved by at least 30%.",
      author: '<PERSON> <PERSON>',
      role: 'Content Operations Manager',
      company: 'ContentScale',
    },
    {
      content:
        "We've been able to identify content gaps and opportunities we never would have found manually. Our organic traffic has doubled in just three months.",
      author: 'Robert Taylor',
      role: 'SEO Director',
      company: 'RankWise Agency',
    },
    {
      content:
        "The AI-driven topic clustering is remarkably accurate. It's helped us create more comprehensive content that truly satisfies search intent.",
      author: 'Olivia Martinez',
      role: 'AI Content Strategist',
      company: 'FutureContent',
    },
    {
      content:
        "As an agency, we now include the SEO Content Matrix in all our client packages. It's become an essential part of our content strategy process.",
      author: 'James Wilson',
      role: 'Agency Founder',
      company: 'Wilson Digital',
    },
    {
      content:
        "The keyword suggestions are incredibly valuable. We've discovered profitable niches we wouldn't have found otherwise.",
      author: 'Aisha Johnson',
      role: 'E-commerce Manager',
      company: 'ShopSmart',
    },
    {
      content:
        "I appreciate how the tool organizes keywords by search intent. It's made our content planning much more strategic and effective.",
      author: 'Daniel Kim',
      role: 'Content Strategist',
      company: 'ContentFirst',
    },
  ];

  // State for the visible testimonials
  const [visibleTestimonials, setVisibleTestimonials] = useState<typeof testimonials>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [touchStartX, setTouchStartX] = useState(0);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const frameCountRef = useRef(0);
  const lastSwapTimeRef = useRef(0);

  // Function to check if we're on mobile
  const checkIfMobile = () => {
    if (typeof window !== 'undefined') {
      setIsMobile(window.innerWidth < 768);
    }
  };

  // Initialize with the first set of testimonials and check device type
  useEffect(() => {
    // Create a circular array of testimonials for infinite scrolling
    const initialTestimonials = [...testimonials.slice(0, 6)];
    setVisibleTestimonials(initialTestimonials);

    // Check if we're on mobile
    checkIfMobile();

    // Add resize listener
    window.addEventListener('resize', checkIfMobile);

    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  // Function to manually advance to the next testimonial
  const showNextTestimonial = useCallback(() => {
    if (!scrollContainerRef.current) return;

    // No need to calculate testimonial width for manual navigation

    // Add the next testimonial and remove the first one
    setVisibleTestimonials(prev => {
      const newArray = [...prev];
      newArray.push(testimonials[(currentIndex + prev.length) % testimonials.length]);
      newArray.shift();
      return newArray;
    });

    // Reset scroll position
    scrollContainerRef.current.scrollLeft = 0;

    // Update the current index
    setCurrentIndex((currentIndex + 1) % testimonials.length);

    // Update the last swap time
    lastSwapTimeRef.current = Date.now();
  }, [currentIndex, isMobile, testimonials]);

  // Function to handle the infinite scroll animation
  const animateScroll = () => {
    if (!scrollContainerRef.current || !autoScrollEnabled || isPaused) {
      // If paused or auto-scroll disabled, just request the next frame without scrolling
      animationRef.current = requestAnimationFrame(animateScroll);
      return;
    }

    // Increment frame counter
    frameCountRef.current += 1;

    // Only scroll on certain frames to slow down the animation
    const frameModulo = isMobile ? 8 : 4; // Even slower on mobile
    if (frameCountRef.current % frameModulo === 0) {
      // Slow, continuous scroll with reduced speed
      scrollContainerRef.current.scrollLeft += isMobile ? 0.1 : 0.15;
    }

    // If we've scrolled to the end of a testimonial, update the array
    const containerWidth = scrollContainerRef.current.clientWidth;
    const scrollPosition = scrollContainerRef.current.scrollLeft;

    // Calculate testimonial width based on screen size
    const visibleItems = isMobile ? 1 : containerWidth < 1024 ? 2 : 3;
    const testimonialWidth = containerWidth / visibleItems;

    // Minimum time between swaps (3 seconds)
    const minSwapInterval = 3000;
    const currentTime = Date.now();
    const timeElapsedSinceLastSwap = currentTime - lastSwapTimeRef.current;

    // When we've scrolled past a testimonial and enough time has passed, add a new one at the end
    if (scrollPosition > testimonialWidth * 0.8 && timeElapsedSinceLastSwap > minSwapInterval) {
      showNextTestimonial();
    }

    // Continue the animation
    animationRef.current = requestAnimationFrame(animateScroll);
  };

  // Timer-based fallback for mobile devices
  useEffect(() => {
    // Only use timer on mobile and when auto-scroll is enabled
    if (isMobile && autoScrollEnabled && !isPaused) {
      // Create a timer that advances testimonials every 5 seconds
      const timer = setInterval(() => {
        showNextTestimonial();
      }, 5000);

      return () => clearInterval(timer);
    }
  }, [isMobile, autoScrollEnabled, isPaused, currentIndex, showNextTestimonial]);

  // Start the animation when the component mounts
  useEffect(() => {
    // Start the animation
    if (animationRef.current === null) {
      animationRef.current = requestAnimationFrame(animateScroll);
    }

    // Clean up the animation when the component unmounts
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    };
  }, [currentIndex, isPaused, autoScrollEnabled, isMobile]);

  // Handle touch events for manual swiping
  const handleTouchStart = (e: React.TouchEvent) => {
    setIsPaused(true);
    setTouchStartX(e.touches[0].clientX);
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    const touchEndX = e.changedTouches[0].clientX;
    const diffX = touchEndX - touchStartX;

    // If swiped left (negative diffX), show next testimonial
    if (diffX < -50) {
      showNextTestimonial();
    }
    // If swiped right (positive diffX), show previous testimonial
    else if (diffX > 50) {
      // Show previous testimonial
      setVisibleTestimonials(prev => {
        const newArray = [...prev];
        const prevIndex = (currentIndex - 1 + testimonials.length) % testimonials.length;
        newArray.unshift(testimonials[prevIndex]);
        newArray.pop();
        return newArray;
      });

      setCurrentIndex((currentIndex - 1 + testimonials.length) % testimonials.length);
    }

    // Resume auto-scrolling after a delay
    setTimeout(() => {
      setIsPaused(false);
    }, 1000);
  };

  // Function to toggle auto-scrolling
  const toggleAutoScroll = () => {
    setAutoScrollEnabled(prev => !prev);
  };

  return (
    <div className="bg-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div>
          {/* Uppercase label - centered on all devices */}
          <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase text-center">Testimonials</h2>
          {/* Main heading - centered on all devices */}
          <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl text-center">
            Trusted by content professionals
          </p>
          {/* Subheading - left-aligned on mobile, centered on lg screens */}
          <p className="mt-4 max-w-2xl text-base sm:text-xl text-gray-500 text-left lg:text-center lg:mx-auto">
            See what our customers have to say about the SEO Content Matrix Generator.
          </p>
        </div>

        {/* Mobile swipe indicator */}

        {/* Auto-scroll toggle for mobile testing */}
        {isMobile && (
          <div className="mt-2 flex justify-center">
            <button
              onClick={toggleAutoScroll}
              className="text-xs text-indigo-600 underline"
            >
              {autoScrollEnabled ? 'Pause Auto-Scroll' : 'Resume Auto-Scroll'}
            </button>
          </div>
        )}

        {/* Manual navigation buttons for mobile */}
        <div className="mt-4 flex justify-between px-4 md:hidden">
          <button
            onClick={() => {
              // Show previous testimonial
              setVisibleTestimonials(prev => {
                const newArray = [...prev];
                const prevIndex = (currentIndex - 1 + testimonials.length) % testimonials.length;
                newArray.unshift(testimonials[prevIndex]);
                newArray.pop();
                return newArray;
              });

              setCurrentIndex((currentIndex - 1 + testimonials.length) % testimonials.length);

              // Pause animation temporarily
              setIsPaused(true);
              setTimeout(() => setIsPaused(false), 1000);
            }}
            className="bg-gray-100 rounded-full p-2 shadow-sm"
            aria-label="Previous testimonial"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={() => {
              showNextTestimonial();

              // Pause animation temporarily
              setIsPaused(true);
              setTimeout(() => setIsPaused(false), 1000);
            }}
            className="bg-gray-100 rounded-full p-2 shadow-sm"
            aria-label="Next testimonial"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        <div
          ref={scrollContainerRef}
          className="mt-6 md:mt-12 flex overflow-x-hidden gap-4 sm:gap-6 md:gap-8 pb-4 scroll-smooth max-w-full"
          onMouseEnter={() => setIsPaused(true)}
          onMouseLeave={() => setIsPaused(false)}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
        >
          {visibleTestimonials.map((testimonial, index) => (
            <div
              key={`${testimonial.author}-${index}`}
              className="flex-shrink-0 w-[85vw] sm:w-[80vw] md:w-1/2 lg:w-1/3 bg-gray-50 rounded-lg shadow-sm p-4 sm:p-6 border border-gray-200 transition-all duration-300 hover:shadow-md"
            >
              <div className="h-full flex flex-col justify-between">
                <div>
                  <svg className="h-8 w-8 text-indigo-400 mb-4" fill="currentColor" viewBox="0 0 32 32">
                    <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                  </svg>
                  <p className="text-gray-600 mb-4 text-sm sm:text-base">{testimonial.content}</p>
                </div>
                <div>
                  <div className="font-medium text-gray-900 text-sm sm:text-base">{testimonial.author}</div>
                  <div className="text-indigo-600 text-xs sm:text-sm">
                    {testimonial.role}, {testimonial.company}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation dots for mobile */}
        <div className="mt-6 flex justify-center space-x-2 md:hidden">
          {[...Array(Math.min(5, testimonials.length))].map((_, i) => (
            <button
              key={i}
              className={`h-2 w-2 rounded-full ${
                i === currentIndex % 5 ? 'bg-indigo-600' : 'bg-gray-300'
              } transition-colors duration-200`}
              aria-label={`Go to testimonial ${i + 1}`}
              onClick={() => {
                if (scrollContainerRef.current) {
                  // Pause animation temporarily
                  setIsPaused(true);

                  // Calculate new index
                  const newIndex = (Math.floor(currentIndex / 5) * 5) + i;
                  setCurrentIndex(newIndex % testimonials.length);

                  // Update visible testimonials
                  const startIdx = newIndex % testimonials.length;
                  const newTestimonials = [];
                  for (let j = 0; j < 6; j++) {
                    newTestimonials.push(testimonials[(startIdx + j) % testimonials.length]);
                  }
                  setVisibleTestimonials(newTestimonials);

                  // Reset scroll position
                  scrollContainerRef.current.scrollLeft = 0;

                  // Resume animation after a short delay
                  setTimeout(() => setIsPaused(false), 1000);
                }
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestimonialsSection;
