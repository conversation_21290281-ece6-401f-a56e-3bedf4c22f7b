'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { handleNavLinkClick } from '@/utils/scrollUtils';

const Footer = () => {
  const pathname = usePathname();
  const isHomePage = pathname === '/';

  // Function to get the correct link based on current page
  const getNavLink = (hash: string) => {
    return isHomePage ? hash : `/${hash}`;
  };
  return (
    <footer className="bg-gray-800 text-white">
      <div className=" mx-auto py-12 px-4 sm:px-6 lg:px-8">
        {/* Company Info - Full width on all screens */}
        <div className="mb-8">
          <div className="flex items-center">
            <Link href="/" className="text-2xl font-bold text-white tracking-tight">
              MATRIX
            </Link>
          </div>
          <p className="mt-4 text-sm text-gray-300">
            Generate SEO-optimized content matrices from keywords to boost your content strategy.
          </p>
          <div className="mt-4 flex space-x-6">
            <a href="javascript:void(0)" className="text-gray-400 hover:text-white">
              <span className="sr-only">Twitter</span>
              <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
              </svg>
            </a>
            <a href="javascript:void(0)" className="text-gray-400 hover:text-white">
              <span className="sr-only">LinkedIn</span>
              <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
              </svg>
            </a>
          </div>
        </div>

        {/* Product and Company sections - Two columns on mobile, part of 4-column grid on larger screens */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
        

          {/* Product */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold text-gray-200 tracking-wider uppercase">Product</h3>
            <ul className="mt-4 space-y-4">
              <li>
                <Link
                  href={getNavLink("#features")}
                  className="text-base text-gray-300 hover:text-white"
                  onClick={(e) => isHomePage && handleNavLinkClick(e, '#features')}
                >
                  Features
                </Link>
              </li>
              <li>
                <Link
                  href={getNavLink("#how-it-works")}
                  className="text-base text-gray-300 hover:text-white"
                  onClick={(e) => isHomePage && handleNavLinkClick(e, '#how-it-works')}
                >
                  How It Works
                </Link>
              </li>
              <li>
                <Link
                  href={getNavLink("#pricing")}
                  className="text-base text-gray-300 hover:text-white"
                  onClick={(e) => isHomePage && handleNavLinkClick(e, '#pricing')}
                >
                  Pricing
                </Link>
              </li>
              <li>
                <Link
                  href={getNavLink("#faq")}
                  className="text-base text-gray-300 hover:text-white"
                  onClick={(e) => isHomePage && handleNavLinkClick(e, '#faq')}
                >
                  FAQ
                </Link>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div className="col-span-1">
            <h3 className="text-sm font-semibold text-gray-200 tracking-wider uppercase">Company</h3>
            <ul className="mt-4 space-y-4">
              <li>
                <a href="/about" className="text-base text-gray-300 hover:text-white">
                  About
                </a>
              </li>
              <li>
                <Link href="/contact" className="text-base text-gray-300 hover:text-white">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-base text-gray-300 hover:text-white">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-base text-gray-300 hover:text-white">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>


        </div>

        <div className="mt-12 border-t border-gray-700 pt-8">
          <p className="text-base text-gray-400 text-center">
            &copy; {new Date().getFullYear()} MATRIX. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
