import Link from 'next/link';
import Image from 'next/image';
import { handleNavLinkClick } from '@/utils/scrollUtils';

const HeroSection = () => {
  return (
    <div className="relative bg-white overflow-hidden">
      <div className=" mx-auto px-6 sm:px-8 lg:px-10">
        <div className="flex flex-col lg:flex-row items-center justify-between">
          {/* Content section - centered on mobile, left-aligned on desktop */}
          <div className="w-full md:w-3/4 lg:w-1/2 lg:pr-12 mb-10 lg:mb-0  lg:text-left mx-auto md:mx-0">
            <h1 className="text-3xl sm:text-5xl font-extrabold tracking-tight md:pt-0 pt-8 lg:text-left md:text-left text-center">
              <span className="block text-gray-900">Generate optimized</span>
              <span className="block text-indigo-600 mt-2">content matrices</span>
            </h1>
            <p className="mt-6 text-base sm:text-lg text-gray-600 max-w-xl">
              Boost your content strategy with our powerful SEO Content
              Matrix Generator. Input your main keyword to get comprehensive
              keyword research and content planning data in seconds.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row">
              <Link
                href="/signup"
                className="w-full sm:w-auto flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Try It Free
              </Link>
              <Link
                href="#how-it-works"
                className="w-full sm:w-auto mt-3 sm:mt-0 sm:ml-4 flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
                onClick={(e) => handleNavLinkClick(e, '#how-it-works')}
              >
                Learn more
              </Link>
            </div>
          </div>

          {/* Right image section - hidden on mobile */}
          <div className="hidden md:block w-full lg:w-1/2 lg:pl-8">
            <div className="relative">
              <Image
                src="/hero.webp"
                alt="SEO Content Matrix Hero"
                width={600}
                height={600}
                priority
                quality={100}
                className="w-full h-auto rounded-lg shadow-xl"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
