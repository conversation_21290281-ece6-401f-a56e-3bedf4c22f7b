import { User } from '@/types';

// Simple JWT token generation function
// In a production environment, you would use a proper JWT library
export function generateToken(user: any): string {
  // Create a payload with user information and expiration
  const payload = {
    id: user._id.toString(),
    email: user.email,
    name: user.name,
    plan: user.plan,
    exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 30), // 30 days expiration
  };

  // In a real implementation, you would use a proper JWT library
  // For simplicity, we're just base64 encoding the payload
  // This is NOT secure for production use
  const base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64');
  
  // In production, you would sign this with a secret key
  // For now, we're just using a simple token format
  return `${base64Payload}.${process.env.JWT_SECRET || 'matrix-secret-key'}`;
}

// Verify and decode a token
export function verifyToken(token: string): any | null {
  try {
    // Split the token to get the payload and signature
    const [payloadBase64] = token.split('.');
    
    // Decode the payload
    const payload = JSON.parse(Buffer.from(payloadBase64, 'base64').toString());
    
    // Check if token is expired
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null; // Token expired
    }
    
    return payload;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
}
