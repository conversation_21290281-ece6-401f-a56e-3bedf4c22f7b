import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Matrix from '@/models/Matrix';
import Project from '@/models/Project';
import { verifyToken } from '@/utils/jwtUtils';
import mongoose from 'mongoose';

export const dynamic = 'force-dynamic';

// Create a new matrix
export async function POST(req: NextRequest) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const body = await req.json();
    const { 
      projectId, 
      mainKeyword, 
      location, 
      language, 
      keywordResearch, 
      contentMatrix 
    } = body;

    if (!projectId || !mainKeyword) {
      return NextResponse.json({ error: 'Project ID and main keyword are required' }, { status: 400 });
    }

    const userId = userData.id;

    // Validate if projectId is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(projectId)) {
      return NextResponse.json({ error: 'Invalid project ID' }, { status: 400 });
    }

    // Verify the project belongs to the user
    const project = await Project.findOne({
      _id: projectId,
      userId,
    });

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Create the matrix
    const matrix = await Matrix.create({
      projectId,
      userId,
      mainKeyword,
      filename: `${mainKeyword.toLowerCase().replace(/\s+/g, '-')}-matrix.json`,
      location: location || 'United States',
      language: language || 'English',
      keywordResearch: keywordResearch || [],
      contentMatrix: contentMatrix || [],
    });

    return NextResponse.json({ matrix }, { status: 201 });
  } catch (error) {
    console.error('Error creating matrix:', error);
    return NextResponse.json({ error: 'Failed to create matrix' }, { status: 500 });
  }
}

// Get all matrices for the authenticated user
export async function GET(req: NextRequest) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const userId = userData.id;
    const matrices = await Matrix.find({ userId }).sort({ createdAt: -1 });

    return NextResponse.json({ matrices });
  } catch (error) {
    console.error('Error fetching matrices:', error);
    return NextResponse.json({ error: 'Failed to fetch matrices' }, { status: 500 });
  }
}
