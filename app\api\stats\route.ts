import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Project from '@/models/Project';
import Matrix from '@/models/Matrix';
import { verifyToken } from '@/utils/jwtUtils';

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const userId = userData.id;

    // Get project count
    const projectCount = await Project.countDocuments({ userId });

    // Get matrices
    const matrices = await Matrix.find({ userId });

    // Count keywords (sum of all keywords in all matrices)
    let keywordCount = 0;
    matrices.forEach(matrix => {
      keywordCount += matrix.keywordResearch?.length || 0;
    });

    // Count clusters (sum of all content matrix items in all matrices)
    let clusterCount = 0;
    matrices.forEach(matrix => {
      clusterCount += matrix.contentMatrix?.length || 0;
    });

    return NextResponse.json({
      projectCount,
      keywordCount,
      clusterCount
    });
  } catch (error) {
    console.error('Error fetching stats:', error);
    return NextResponse.json({ error: 'Failed to fetch stats' }, { status: 500 });
  }
}
