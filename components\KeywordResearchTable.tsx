import React from 'react';

interface KeywordResearchItem {
  mainKeyword: string;
  type: string;
  keyword: string;
  msv: number;
  searchIntent: string;
  kwDifficulty: number;
  competition: number;
  cpc: number;
  answer: string;
  timestamp: string;
}

interface KeywordResearchTableProps {
  data: KeywordResearchItem[];
  isLoading?: boolean;
}

const KeywordResearchTable: React.FC<KeywordResearchTableProps> = ({ data, isLoading = false }) => {
  if (isLoading) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Keyword Research</h2>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Keyword Research</h2>
        <p className="text-gray-500 italic">No data available. Please submit the form to generate keyword research.</p>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg p-4 sm:p-6 mb-8">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Keyword Research</h2>

      {/* Desktop view - traditional table */}
      <div className="hidden md:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Main Keyword</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keyword</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MSV</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Search Intent</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KW Difficulty</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Competition</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CPC</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Answer</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.mainKeyword}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.type}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.keyword}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.msv}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.searchIntent}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.kwDifficulty}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.competition}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.cpc}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.answer}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.timestamp}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile view - card layout */}
      <div className="md:hidden space-y-4">
        <div className="text-sm text-gray-500 mb-2 flex justify-between items-center">
          <span>Showing {data.length} results</span>
          <div className="text-indigo-600 text-xs">
            Swipe cards horizontally to see more →
          </div>
        </div>

        {data.map((item, index) => (
          <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-sm p-4 overflow-x-auto">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-medium text-gray-900">{item.keyword}</h3>
              <span className="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">{item.type}</span>
            </div>

            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="block text-gray-500 text-xs">Main Keyword</span>
                <span className="font-medium">{item.mainKeyword}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">MSV</span>
                <span className="font-medium">{item.msv}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">Search Intent</span>
                <span className="font-medium">{item.searchIntent}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">KW Difficulty</span>
                <span className="font-medium">{item.kwDifficulty}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">Competition</span>
                <span className="font-medium">{item.competition}</span>
              </div>
              <div>
                <span className="block text-gray-500 text-xs">CPC</span>
                <span className="font-medium">{item.cpc}</span>
              </div>
            </div>

            <div className="mt-3 pt-3 border-t border-gray-100">
              <span className="block text-gray-500 text-xs">Answer</span>
              <p className="text-sm text-gray-900 mt-1">{item.answer}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default KeywordResearchTable;
