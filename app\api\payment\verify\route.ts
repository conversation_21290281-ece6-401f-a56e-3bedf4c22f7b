import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { verifyPaymentStatus } from '@/utils/cashfreeSdk';
import {
  logPaymentEvent,
  PAYMENT_STATUS,
  DB_PAYMENT_STATUS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
} from '@/utils/cashfreeConfig';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    logPaymentEvent('PAYMENT_VERIFY_REQUEST', { url: request.url });

    const paymentData = await request.json();

    // Extract data from the payment data
    const orderId = paymentData.order_id || paymentData.orderId;
    let referenceId = paymentData.transaction_id || paymentData.referenceId;
    let txStatus = paymentData.transaction_status || paymentData.txStatus || PAYMENT_STATUS.SUCCESS;

    logPaymentEvent('PAYMENT_VERIFY_DATA', {
      orderId,
      referenceId: referenceId ? referenceId.substring(0, 8) + '...' : 'null',
      txStatus
    });

    // Validate input
    if (!orderId) {
      logPaymentEvent('PAYMENT_VERIFY_INVALID_PARAMS', { orderId });
      return NextResponse.json(
        { success: false, message: 'Invalid payment data: missing order ID' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Find the user with this order ID
    let user;

    if (paymentData.userId) {
      user = await User.findById(paymentData.userId);
    } else {
      user = await User.findOne({ paymentOrderId: orderId });
    }

    if (!user) {
      logPaymentEvent('PAYMENT_VERIFY_USER_NOT_FOUND', { orderId });

      // User not found is an error
      return NextResponse.json(
        { success: false, message: ERROR_MESSAGES.USER_NOT_FOUND },
        { status: 404 }
      );
    }

    // ALWAYS verify the payment status with Cashfree API
    let orderIsPaid = false;

    try {
      // Always get the order details first - this is the most reliable way to check payment status
      const { getOrderDetails } = await import('@/utils/cashfreeSdk');
      const orderDetails = await getOrderDetails(orderId);

      if (orderDetails && orderDetails.order_status) {
        // Check if the order is paid - this is the most reliable indicator
        if (orderDetails.order_status === 'PAID') {
          orderIsPaid = true;
          txStatus = PAYMENT_STATUS.SUCCESS;

          // If there's a payment, get the reference ID
          if (orderDetails.payments && orderDetails.payments.length > 0) {
            const payment = orderDetails.payments[0];
            paymentData.referenceId = payment.cf_payment_id || payment.payment_id;
            referenceId = paymentData.referenceId;
          }
        } else if (orderDetails.order_status === 'ACTIVE') {
          // Order is active but not paid yet
          txStatus = PAYMENT_STATUS.PENDING;
        } else {
          // Any other status is considered failed
          txStatus = PAYMENT_STATUS.FAILED;
        }

        logPaymentEvent('PAYMENT_VERIFY_ORDER_DETAILS', {
          orderId,
          orderStatus: orderDetails.order_status,
          isPaid: orderIsPaid,
          txStatus
        });
      }

      // If we have a reference ID and the order is not marked as paid, also verify the specific payment
      if (referenceId && !orderIsPaid) {
        try {
          const paymentVerification = await verifyPaymentStatus(orderId, referenceId);

          // Use the verification result to determine the status
          if (paymentVerification.success) {
            orderIsPaid = true;
            txStatus = PAYMENT_STATUS.SUCCESS;
          }

          logPaymentEvent('PAYMENT_VERIFY_API_RESULT', {
            orderId,
            status: paymentVerification.status,
            mode: paymentVerification.mode,
            isPaid: orderIsPaid
          });
        } catch (paymentVerifyError) {
          logPaymentEvent('PAYMENT_VERIFICATION_ERROR', {
            orderId,
            error: paymentVerifyError instanceof Error ? paymentVerifyError.message : 'Unknown error'
          });
        }
      }
    } catch (verifyError) {
      // Log the error but continue with the provided status
      logPaymentEvent('PAYMENT_VERIFY_API_ERROR', {
        orderId,
        error: verifyError instanceof Error ? verifyError.message : 'Unknown error'
      });
    }

    // Update user's payment status based on order verification
    // ONLY consider the payment successful if the order status is PAID or we have explicit confirmation
    if (orderIsPaid || txStatus === PAYMENT_STATUS.SUCCESS) {
      user.paymentStatus = DB_PAYMENT_STATUS.COMPLETED;
      user.paymentReference = referenceId || 'verify_reference';
      user.paymentMode = paymentData.paymentMode || 'card';
      user.paymentTime = new Date();
      await user.save();

      logPaymentEvent('PAYMENT_VERIFY_SUCCESS_DB_UPDATED', {
        orderId,
        userId: user._id,
        plan: user.paymentPlan || user.plan
      });

      return NextResponse.json({
        success: true,
        message: SUCCESS_MESSAGES.PAYMENT_VERIFIED,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          plan: user.plan,
          paymentStatus: user.paymentStatus,
          createdAt: user.createdAt,
        },
      });
    } else {
      user.paymentStatus = DB_PAYMENT_STATUS.FAILED;
      user.paymentReference = referenceId || 'verify_reference';
      user.paymentFailureReason = paymentData.txMsg || 'Payment failed or cancelled';
      await user.save();

      logPaymentEvent('PAYMENT_VERIFY_FAILURE_DB_UPDATED', {
        orderId,
        userId: user._id,
        status: txStatus
      });

      return NextResponse.json(
        { success: false, message: 'Payment failed or cancelled' },
        { status: 400 }
      );
    }
  } catch (error) {
    logPaymentEvent('PAYMENT_VERIFY_ERROR', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return NextResponse.json(
      { success: false, message: ERROR_MESSAGES.PAYMENT_VERIFICATION_FAILED },
      { status: 500 }
    );
  }
}
