'use client';

import AppLayout from '@/components/AppLayout';
import Image from 'next/image';
import Link from 'next/link';

// Metadata is handled differently in client components
// This would be in the layout.tsx file in a real app

export default function AboutPage() {
  return (
    <AppLayout>
      <main className="pt-8 pb-12 bg-gray-50">
        {/* Hero Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
          <div className="text-center">
            <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl">
              About Us
            </h1>
            <p className="max-w-xl mt-5 mx-auto text-xl text-gray-500">
              We're on a mission to simplify content planning with automated keyword clustering and ready-made content maps for businesses of all sizes.
            </p>
          </div>
        </div>

        {/* Our Mission Section */}
        <div id="our-mission" className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Our Mission
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              <div className="flex flex-col items-center">
                <div className="bg-indigo-100 p-4 rounded-full mb-4">
                  <svg className="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Democratizing SEO Knowledge</h3>
                <p className="text-center text-gray-600">
                  Making complex SEO concepts and content planning accessible to everyone, regardless of technical background.
                </p>
              </div>
              <div className="flex flex-col items-center">
                <div className="bg-indigo-100 p-4 rounded-full mb-4">
                  <svg className="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Data-Driven Insights</h3>
                <p className="text-center text-gray-600">
                  Providing content recommendations based on real search data and user intent analysis.
                </p>
              </div>
              <div className="flex flex-col items-center">
                <div className="bg-indigo-100 p-4 rounded-full mb-4">
                  <svg className="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Content Strategy Innovation</h3>
                <p className="text-center text-gray-600">
                  Developing advanced clustering algorithms that transform keyword research into actionable content plans.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Our Vision Section */}
        <div id="our-vision" className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Our Vision
              </h2>
            </div>
            <div className="prose prose-lg mx-auto">
              <p className="text-center text-gray-600 mb-8">
                At MATRIX, we envision a future where creating effective content strategies is simple, intuitive, and accessible to everyone.
                Our goal is to bridge the gap between complex SEO concepts and practical implementation, empowering businesses
                to reach their full potential online through strategic content planning.
              </p>
              <p className="text-center text-gray-600 mb-8">
                We believe in a world where content strategy is inclusive, where businesses of all sizes can
                harness the power of cutting-edge SEO tools without requiring specialized expertise. Through
                our automated keyword clustering and content mapping, we're working to make this
                vision a reality.
              </p>
              <p className="text-center text-gray-600">
                We strive to be at the forefront of SEO innovation while ensuring that the benefits of
                these advancements are accessible to all. We're committed to creating a more effective, efficient,
                and results-driven content planning process for businesses of every size.
              </p>
            </div>
          </div>
        </div>

        {/* Core Values Section */}
        <div id="core-values" className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Our Core Values
              </h2>
              <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-500">
                The principles that guide everything we do
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
              <div className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white mb-4 mx-auto">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 text-center mb-2">Simplicity</h3>
                <p className="text-gray-600 text-center">
                  We believe in making complex SEO concepts simple and accessible for everyone.
                </p>
              </div>
              <div className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white mb-4 mx-auto">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 text-center mb-2">Reliability</h3>
                <p className="text-gray-600 text-center">
                  We deliver accurate, up-to-date SEO insights you can trust to grow your business.
                </p>
              </div>
              <div className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white mb-4 mx-auto">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 text-center mb-2">Accessibility</h3>
                <p className="text-gray-600 text-center">
                  We ensure our tools are accessible to businesses of all sizes and technical abilities.
                </p>
              </div>
              <div className="bg-gray-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white mb-4 mx-auto">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 text-center mb-2">Innovation</h3>
                <p className="text-gray-600 text-center">
                  We continuously improve our tools to stay ahead of evolving SEO best practices.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Why We Built This Tool Section */}
        <div id="why-we-built" className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Why We Built This Tool
              </h2>
            </div>
            <div className="bg-white shadow-lg rounded-lg overflow-hidden max-w-4xl mx-auto">
              <div className="p-8">
                <p className="text-gray-600 mb-6">
                  We created MATRIX after witnessing countless businesses struggle with content planning.
                  Many were overwhelmed by the complexity of keyword research, unsure how to organize their findings into coherent topic clusters,
                  or simply didn't have the time to manually create comprehensive content strategies that align with search intent.
                </p>
                <p className="text-gray-600 mb-6">
                  Our team of SEO experts and developers came together with a shared vision: to automate the most
                  time-consuming aspects of content planning while maintaining the strategic insights that drive organic traffic and conversions.
                </p>
                <p className="text-gray-600">
                  The result is our MATRIX tool that handles the research grunt work using live data, automatically clusters keywords
                  by search intent, and provides ready-made content maps with clear pillar and supporting content structures—all without requiring any technical SEO knowledge.
                </p>
              </div>
            </div>
          </div>
        </div>



        {/* CTA Section */}
        <div className="bg-indigo-700 py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 lg:flex lg:items-center lg:justify-between">
            <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
              <span className="block">Ready to transform your content strategy?</span>
              <span className="block text-indigo-200">Start using MATRIX today.</span>
            </h2>
            <div className="mt-8 flex lg:mt-0 lg:flex-shrink-0">
              <div className="inline-flex rounded-md shadow">
                <Link
                  href="/signup"
                  className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50"
                >
                  Try It Free
                </Link>
              </div>
              <div className="ml-3 inline-flex rounded-md shadow">
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  Contact us
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </AppLayout>
  );
}
