'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { SignUpFormData, PlanType } from '@/types';
import { useAuth } from '@/context/AuthContext';
import OTPVerificationForm from './OTPVerificationForm';

const SignUpForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signUp } = useAuth();
  const [formData, setFormData] = useState<SignUpFormData>({
    name: '',
    email: searchParams?.get('email') || '',
    plan: 'free', // Default to free plan
  });

  // Effect to handle URL parameters
  useEffect(() => {
    const emailParam = searchParams?.get('email');
    if (emailParam) {
      setFormData(prev => ({ ...prev, email: emailParam }));
    }
  }, [searchParams]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);

    // Validate email
    if (!formData.email || !/^\S+@\S+\.\S+$/.test(formData.email)) {
      setError('Please enter a valid email address');
      return;
    }

    // Validate name
    if (!formData.name.trim()) {
      setError('Please enter your name');
      return;
    }

    setIsLoading(true);

    try {
      const { name, email, plan } = formData;
      const result = await signUp(name, email, plan);

      if (result.success) {
        setOtpSent(true);
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Sign up error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    setOtpSent(false);
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      {otpSent ? (
        <OTPVerificationForm email={formData.email} onBack={handleBack} authFlow="signup" />
      ) : (
        <>
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Create an Account</h2>

          {error && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Full Name
              </label>
              <input
                id="name"
                name="name"
                type="text"
                autoComplete="name"
                required
                value={formData.name}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>

            <div>
              <label htmlFor="plan" className="block text-sm font-medium text-gray-700">
                Select Plan
              </label>
              <select
                id="plan"
                name="plan"
                value={formData.plan}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="free">Free Plan - $0/month</option>
                <option value="standard">Standard Plan - $20/month</option>
                <option value="pro">Pro Plan - $49/month</option>
              </select>
              <p className="mt-2 text-xs text-gray-500">
                {formData.plan === 'free' && "Basic features with limited usage."}
                {formData.plan === 'standard' && "All essential features for small businesses."}
                {formData.plan === 'pro' && "Full access to all premium features."}
              </p>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {isLoading ? 'Sending OTP...' : 'Continue with OTP'}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <p className="text-center text-sm text-gray-600">
              Already have an account?{' '}
              <Link href="/login-with-otp" className="font-medium text-indigo-600 hover:text-indigo-500">
                Sign in
              </Link>
            </p>
          </div>
        </>
      )}
    </div>
  );
};

export default SignUpForm;
