# MATRIX

A Next.js application that helps users generate SEO-optimized content matrices from keywords. This tool allows you to input a main keyword and generates comprehensive keyword research and content matrix data.

## Features

- Input form for main keyword,
- Keyword Research table with detailed metrics
- Content Matrix table with content planning information
- Responsive design with Tailwind CSS
- Webhook API endpoint for receiving external data

## Technologies Used

- Next.js 14
- TypeScript
- Tailwind CSS
- React Hooks

## Getting Started

First, install the dependencies:

```bash
npm install
```

Then, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the application.

## API Endpoints

### Webhook Endpoint

The application includes a webhook endpoint that can receive SEO data from external sources.

**Endpoint:** `/api/webhook`

**Method:** POST

**Payload Example:**

```json
{
  "mainKeyword": "digital marketing",
  "location": "United States",
  "language": "English",
  "limit": 10,
  "date": "2023-05-12",
  "timestamp": "2023-05-12T12:00:00Z",
  "keywordResearch": [
    {
      "mainKeyword": "digital marketing",
      "type": "Informational",
      "keyword": "digital marketing best practices",
      "msv": 5000,
      "searchIntent": "Learn",
      "kwDifficulty": 45,
      "competition": 0.75,
      "cpc": 4.50,
      "answer": "Featured snippet available",
      "timestamp": "2023-05-12T12:00:00Z"
    }
  ],
  "contentMatrix": [
    {
      "mainKeyword": "digital marketing",
      "cluster": "Beginner",
      "contentType": "Blog Post",
      "focus": "digital marketing tips",
      "category": "SEO",
      "url": "/blog/digital-marketing-tips",
      "searchVol": 2500,
      "keywords": "digital marketing, digital marketing tips, best digital marketing",
      "status": "Published"
    }
  ]
}
```

## Demo Data

For demonstration purposes, the application generates mock data when you submit the form. In a production environment, you would connect this to your actual SEO data sources.
