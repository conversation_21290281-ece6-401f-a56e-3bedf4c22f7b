'use client';

import { useEffect } from 'react';

const FeaturesSection = () => {
  const features = [
    {
      name: 'Effortless Keyword Discovery',
      description:
        'We handle the research grunt work using live data. You get high-opportunity keywords, clearly grouped for structured content planning.',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
          <path d="M11.47 3.84a.75.75 0 011.06 0l8.69 8.69a.75.75 0 101.06-1.06l-8.689-8.69a2.25 2.25 0 00-3.182 0l-8.69 8.69a.75.75 0 001.061 1.06l8.69-8.69z" />
          <path d="M12 5.432l8.159 8.159c.************.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 01-.75-.75v-4.5a.75.75 0 00-.75-.75h-3a.75.75 0 00-.75.75V21a.75.75 0 01-.75.75H5.625a1.875 1.875 0 01-1.875-1.875v-6.198a2.29 2.29 0 00.091-.086L12 5.43z" />
        </svg>
      ),
      color: 'from-amber-400 to-orange-500',
      shadowColor: 'shadow-amber-300/40',
    },
    {
      name: 'Intent-Aligned Grouping',
      description:
        'We automatically cluster keywords by search intent, helping you plan pillar and supporting articles that truly match what people are searching for.',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
          <path fillRule="evenodd" d="M2.25 13.5a8.25 8.25 0 018.25-********** 0 01.75.75v6.75H18a.75.75 0 01.75.75 8.25 8.25 0 01-16.5 0z" clipRule="evenodd" />
          <path fillRule="evenodd" d="M12.75 3a.75.75 0 01.75-.75 8.25 8.25 0 018.25 ********** 0 01-.75.75h-7.5a.75.75 0 01-.75-.75V3z" clipRule="evenodd" />
        </svg>
      ),
      color: 'from-blue-400 to-indigo-500',
      shadowColor: 'shadow-blue-300/40',
    },
    {
      name: 'Local Topics Made Easy',
      description:
        'Focus your content by region or language. We surface keyword clusters that align with your target markets—no manual sorting required.',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
          <path fillRule="evenodd" d="M11.54 22.351l.07.04.028.016a.76.76 0 00.723 0l.028-.015.071-.041a16.975 16.975 0 001.144-.742 19.58 19.58 0 002.683-2.282c1.944-1.99 3.963-4.98 3.963-8.827a8.25 8.25 0 00-16.5 0c0 3.846 2.02 6.837 3.963 8.827a19.58 19.58 0 002.682 2.282 16.975 16.975 0 001.145.742zM12 13.5a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
        </svg>
      ),
      color: 'from-green-400 to-emerald-500',
      shadowColor: 'shadow-green-300/40',
    },
    {
      name: 'Ready-Made Content Maps',
      description:
        'Get clean, actionable content structures: pillar topics, supporting content, and subtopics—automatically mapped from your keyword list.',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
          <path fillRule="evenodd" d="M2.625 6.75a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0zm4.875 0A.75.75 0 018.25 6h12a.75.75 0 010 1.5h-12a.75.75 0 01-.75-.75zM2.625 12a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0zM7.5 12a.75.75 0 01.75-.75h12a.75.75 0 010 1.5h-12A.75.75 0 017.5 12zm-4.875 5.25a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0zm4.875 0a.75.75 0 01.75-.75h12a.75.75 0 010 1.5h-12a.75.75 0 01-.75-.75z" clipRule="evenodd" />
        </svg>
      ),
      color: 'from-purple-400 to-indigo-500',
      shadowColor: 'shadow-purple-300/40',
    },
    {
      name: 'One-Click Content Clustering',
      description:
        'Input your keywords. Instantly receive optimized clusters with clear content titles—ready for writing, no spreadsheet gymnastics needed.',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
          <path d="M5.566 4.657A4.505 4.505 0 016.75 4.5h10.5c.41 0 .806.055 1.183.157A3 3 0 0015.75 3h-7.5a3 3 0 00-2.684 1.657zM2.25 12a3 3 0 013-3h13.5a3 3 0 013 3v6a3 3 0 01-3 3H5.25a3 3 0 01-3-3v-6zM5.25 7.5c-.41 0-.806.055-1.184.157A3 3 0 016.75 6h10.5a3 3 0 012.683 1.657A4.505 4.505 0 0018.75 7.5H5.25z" />
        </svg>
      ),
      color: 'from-pink-400 to-rose-500',
      shadowColor: 'shadow-pink-300/40',
    },
    {
      name: 'No SEO or Tech Knowledge Needed',
      description:
        'We\'ve stripped away the complexity. Just enter your keywords and get structured, SEO-smart content ideas—no technical setup required.',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
          <path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
        </svg>
      ),
      color: 'from-teal-400 to-cyan-500',
      shadowColor: 'shadow-teal-300/40',
    },
  ];

  // Add animation effect when component mounts
  useEffect(() => {
    const featureElements = document.querySelectorAll('.feature-item');

    featureElements.forEach((element, index) => {
      setTimeout(() => {
        element.classList.add('animate-in');
      }, index * 150); // Stagger the animations
    });
  }, []);

  return (
    <div id="features" className="py-12 bg-white scroll-mt-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div>
          {/* Uppercase label - centered on all devices */}
          <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase text-center">Features</h2>
          {/* Main heading - centered on all devices */}
          <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl text-center">
            Simplify Your SEO Content Strategy
          </p>
          {/* Subheading - left-aligned on mobile, centered on lg screens */}
          <p className="mt-4 max-w-2xl text-base sm:text-xl text-gray-500 text-left lg:text-center lg:mx-auto">
            Our powerful tools handle the complex work, so you can focus on creating content that ranks and converts.
          </p>
        </div>

        <div className="mt-16">
          <div className="space-y-12 md:space-y-0 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-x-8 md:gap-y-12">
            {features.map((feature) => (
              <div
                key={feature.name}
                className="feature-item relative group opacity-0 transform translate-y-8 transition-all duration-500 ease-out"
              >
                <div className={`
                  absolute flex items-center justify-center lg:h-14 lg:w-14 h-10 w-10 rounded-xl
                  bg-gradient-to-br ${feature.color} text-white
                  shadow-lg ${feature.shadowColor}
                  transition-all duration-300 ease-in-out group-hover:scale-110
                  transform hover:rotate-3
                `}>
                  <div className="relative z-10">
                    {feature.icon}
                  </div>
                  <div className="absolute inset-0 bg-white opacity-20 rounded-xl blur-sm transform scale-90"></div>
                </div>
                <div className="ml-16">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 group-hover:text-indigo-600 transition-colors duration-200">
                    {feature.name}
                  </h3>
                  <p className="mt-2 text-base text-gray-500">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturesSection;
