import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import mongoose from 'mongoose';

export const dynamic = 'force-dynamic';

// Define a schema for payment history
const PaymentHistorySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  orderId: {
    type: String,
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  plan: {
    type: String,
    enum: ['free', 'standard', 'pro'],
    required: true,
  },
  billingCycle: {
    type: String,
    enum: ['monthly', 'annual'],
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed'],
    required: true,
  },
  paymentMethod: String,
  referenceId: String,
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Create or get the model
const PaymentHistory = mongoose.models.PaymentHistory ||
  mongoose.model('PaymentHistory', PaymentHistorySchema);

// GET payment history for a user
export async function GET(request: NextRequest) {
  try {
    // Get user ID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    // Validate input
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Find the user to verify they exist
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // For users with no payment history yet, create a mock entry based on their current plan
    let payments = await PaymentHistory.find({ userId }).sort({ createdAt: -1 });

    // If no payment history and user has a paid plan with completed status, create a mock entry
    if (payments.length === 0 &&
        user.plan !== 'free' &&
        user.paymentStatus === 'completed' &&
        user.paymentOrderId) {

      // Create a mock payment history entry based on user data
      const mockPayment = {
        userId: user._id,
        orderId: user.paymentOrderId,
        amount: user.paymentAmount || (user.plan === 'standard' ? 20 : 49),
        plan: user.plan,
        billingCycle: user.paymentBillingCycle || 'monthly',
        status: 'completed',
        paymentMethod: user.paymentMode || 'card',
        referenceId: user.paymentReference || 'initial-payment',
        createdAt: user.paymentTime || user.createdAt,
      };

      // Save the mock payment to the database
      await PaymentHistory.create(mockPayment);

      // Refresh the payments list
      payments = await PaymentHistory.find({ userId }).sort({ createdAt: -1 });
    }

    // Return payment history
    return NextResponse.json({
      success: true,
      payments: payments.map(payment => ({
        id: payment._id,
        orderId: payment.orderId,
        amount: payment.amount,
        plan: payment.plan,
        billingCycle: payment.billingCycle,
        status: payment.status,
        paymentMethod: payment.paymentMethod,
        referenceId: payment.referenceId,
        createdAt: payment.createdAt,
      })),
    });
  } catch (error) {
    console.error('Error fetching payment history:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch payment history' },
      { status: 500 }
    );
  }
}
