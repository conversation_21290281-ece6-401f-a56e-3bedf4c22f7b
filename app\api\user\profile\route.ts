import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';

export const dynamic = 'force-dynamic';

// GET user profile data
export async function GET(request: NextRequest) {
  try {
    // Get user ID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    // Validate input
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Calculate subscription expiry date if not set
    let expiryDate = user.paymentExpiryDate;
    if (!expiryDate && user.paymentTime && user.paymentBillingCycle) {
      expiryDate = new Date(user.paymentTime);
      if (user.paymentBillingCycle === 'monthly') {
        expiryDate.setMonth(expiryDate.getMonth() + 1);
      } else if (user.paymentBillingCycle === 'annual') {
        expiryDate.setFullYear(expiryDate.getFullYear() + 1);
      }
    }

    // Return user data
    return NextResponse.json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone || '',
        plan: user.plan,
        paymentStatus: user.paymentStatus,
        paymentBillingCycle: user.paymentBillingCycle || 'monthly',
        paymentExpiryDate: expiryDate || null,
        createdAt: user.createdAt,
      },
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch user profile' },
      { status: 500 }
    );
  }
}

// Update user profile
export async function PUT(request: NextRequest) {
  try {
    const { userId, name, email, phone } = await request.json();

    // Validate input
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Update user data
    if (name) user.name = name;
    if (email) user.email = email;
    if (phone !== undefined) user.phone = phone;

    await user.save();

    // Return updated user data
    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone || '',
        plan: user.plan,
        paymentStatus: user.paymentStatus,
        createdAt: user.createdAt,
      },
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update user profile' },
      { status: 500 }
    );
  }
}
