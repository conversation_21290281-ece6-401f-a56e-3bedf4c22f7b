import mongoose, { Schema, Document } from 'mongoose';

export type PlanType = 'free' | 'standard' | 'pro';
export type BillingCycleType = 'monthly' | 'annual';
export type PaymentStatusType = 'pending' | 'completed' | 'failed';

export interface IUser extends Document {
  name: string;
  email: string;
  phone?: string;
  plan: PlanType;
  paymentStatus?: PaymentStatusType;
  paymentOrderId?: string;
  paymentReference?: string;
  paymentMode?: string;
  paymentTime?: Date;
  paymentAmount?: number;
  paymentPlan?: PlanType;
  paymentBillingCycle?: BillingCycleType;
  paymentCreatedAt?: Date;
  paymentFailureReason?: string;
  paymentExpiryDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: [true, 'Please provide a name'],
      maxlength: [60, 'Name cannot be more than 60 characters'],
    },
    email: {
      type: String,
      required: [true, 'Please provide an email'],
      unique: true,
      lowercase: true,
      trim: true,
      validate: {
        validator: function (v: string) {
          return /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v);
        },
        message: 'Please enter a valid email',
      },
    },
    phone: {
      type: String,
      trim: true,
      sparse: true,
    },
    plan: {
      type: String,
      enum: ['free', 'standard', 'pro'],
      default: 'free',
      required: [true, 'Please select a plan'],
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
      default: function(this: any) {
        // If plan is free, payment is automatically completed
        return this.plan === 'free' ? 'completed' : 'pending';
      },
    },
    paymentOrderId: {
      type: String,
      sparse: true,
    },
    paymentReference: {
      type: String,
      sparse: true,
    },
    paymentMode: {
      type: String,
      sparse: true,
    },
    paymentTime: {
      type: Date,
      sparse: true,
    },
    paymentAmount: {
      type: Number,
      sparse: true,
    },
    paymentPlan: {
      type: String,
      enum: ['free', 'standard', 'pro'],
      sparse: true,
    },
    paymentBillingCycle: {
      type: String,
      enum: ['monthly', 'annual'],
      default: 'monthly',
      sparse: true,
    },
    paymentCreatedAt: {
      type: Date,
      sparse: true,
    },
    paymentFailureReason: {
      type: String,
      sparse: true,
    },
    paymentExpiryDate: {
      type: Date,
      sparse: true,
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
