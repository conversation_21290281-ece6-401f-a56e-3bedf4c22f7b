import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Matrix from '@/models/Matrix';
import Project from '@/models/Project';
import { verifyToken } from '@/utils/jwtUtils';
import mongoose from 'mongoose';

export const dynamic = 'force-dynamic';

// Get a specific matrix
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const userId = userData.id;
    const matrixId = params.id;

    // Validate if matrixId is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(matrixId)) {
      return NextResponse.json({ error: 'Invalid matrix ID' }, { status: 400 });
    }

    // Find the matrix and populate project data
    const matrix = await Matrix.findOne({
      _id: matrixId,
      userId,
    }).populate('projectId');

    if (!matrix) {
      return NextResponse.json({ error: 'Matrix not found' }, { status: 404 });
    }

    return NextResponse.json({ matrix });
  } catch (error) {
    console.error('Error fetching matrix:', error);
    return NextResponse.json({ error: 'Failed to fetch matrix' }, { status: 500 });
  }
}

// Update a specific matrix
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const body = await req.json();
    const userId = userData.id;
    const matrixId = params.id;

    // Validate if matrixId is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(matrixId)) {
      return NextResponse.json({ error: 'Invalid matrix ID' }, { status: 400 });
    }

    // Update the matrix
    const matrix = await Matrix.findOneAndUpdate(
      { _id: matrixId, userId },
      { ...body, updatedAt: new Date() },
      { new: true }
    );

    if (!matrix) {
      return NextResponse.json({ error: 'Matrix not found' }, { status: 404 });
    }

    return NextResponse.json({ matrix });
  } catch (error) {
    console.error('Error updating matrix:', error);
    return NextResponse.json({ error: 'Failed to update matrix' }, { status: 500 });
  }
}

// Delete a specific matrix
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const userId = userData.id;
    const matrixId = params.id;

    // Validate if matrixId is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(matrixId)) {
      return NextResponse.json({ error: 'Invalid matrix ID' }, { status: 400 });
    }

    // Delete the matrix
    const matrix = await Matrix.findOneAndDelete({
      _id: matrixId,
      userId,
    });

    if (!matrix) {
      return NextResponse.json({ error: 'Matrix not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Matrix deleted successfully' });
  } catch (error) {
    console.error('Error deleting matrix:', error);
    return NextResponse.json({ error: 'Failed to delete matrix' }, { status: 500 });
  }
}
