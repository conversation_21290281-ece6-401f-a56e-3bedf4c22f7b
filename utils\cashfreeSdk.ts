/**
 * Cashfree SDK Utilities
 *
 * This file contains utility functions for integrating with the Cashfree payment gateway
 * using the official Cashfree Node.js SDK.
 */

import { CashfreeOrderRequest, CashfreeOrderResponse } from './cashfreeUtils';
import {
  isProduction,
  CASHFREE_APP_ID,
  CASHFREE_SECRET_KEY,
  CASHFREE_API_VERSION,
  CASHFREE_API_URL,
  logPaymentEvent
} from './cashfreeConfig';

// We'll use direct API calls instead of the SDK to avoid type issues
const getHeaders = () => {
  return {
    'x-client-id': CASHFREE_APP_ID,
    'x-client-secret': CASHFREE_SECRET_KEY,
    'x-api-version': CASHFREE_API_VERSION,
    'Content-Type': 'application/json',
  };
};

/**
 * Create a new order with Cashfree using direct API calls
 * @param orderData Order data to be sent to Cashfree
 * @returns Order response from Cashfree
 */
export const createOrderWithSdk = async (orderData: CashfreeOrderRequest): Promise<CashfreeOrderResponse> => {
  try {
    logPaymentEvent('CREATE_ORDER_REQUEST', {
      order_id: orderData.order_id,
      order_amount: orderData.order_amount,
      order_currency: orderData.order_currency,
      customer_id: orderData.customer_details.customer_id,
      return_url: orderData.order_meta?.return_url
    });

    // Prepare request for Cashfree API
    const request = {
      order_id: orderData.order_id,
      order_amount: orderData.order_amount,
      order_currency: orderData.order_currency,
      customer_details: {
        customer_id: orderData.customer_details.customer_id,
        customer_name: orderData.customer_details.customer_name,
        customer_email: orderData.customer_details.customer_email,
        customer_phone: orderData.customer_details.customer_phone || '9999999999' // Required by Cashfree
      },
      order_meta: {
        return_url: orderData.order_meta?.return_url || '',
        notify_url: orderData.order_meta?.notify_url,
        payment_methods: orderData.order_meta?.payment_methods
      },
      order_note: orderData.order_note || ''
    };

    // Call Cashfree API directly
    const response = await fetch(`${CASHFREE_API_URL}/orders`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Cashfree API error: ${errorData.message || response.statusText}`);
    }

    const data = await response.json();

    logPaymentEvent('CREATE_ORDER_RESPONSE', {
      order_id: data.order_id,
      cf_order_id: data.cf_order_id,
      order_status: data.order_status,
      payment_session_id: data.payment_session_id
    });

    // Format response to match our interface
    const cashfreeResponse: CashfreeOrderResponse = {
      cf_order_id: data.cf_order_id,
      order_id: data.order_id,
      entity: data.entity,
      order_currency: data.order_currency,
      order_amount: data.order_amount,
      order_status: data.order_status,
      payment_session_id: data.payment_session_id,
      order_expiry_time: data.order_expiry_time,
      order_note: data.order_note,
      payments: data.payments,
      refunds: data.refunds,
      settlements: data.settlements,
      order_token: data.payment_session_id // Use payment_session_id as order_token
    };

    return cashfreeResponse;
  } catch (error) {
    // Log the error
    logPaymentEvent('CREATE_ORDER_ERROR', {
      error: error instanceof Error ? error.message : 'Unknown error',
      order_id: orderData.order_id
    });

    // In production, rethrow the error
    throw error;
  }
};

/**
 * Get order details from Cashfree
 * @param orderId Order ID to fetch
 * @returns Order details from Cashfree
 */
export const getOrderDetails = async (orderId: string) => {
  try {
    logPaymentEvent('GET_ORDER_REQUEST', { order_id: orderId });

    // Call Cashfree API directly
    const response = await fetch(`${CASHFREE_API_URL}/orders/${orderId}`, {
      method: 'GET',
      headers: getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Cashfree API error: ${errorData.message || response.statusText}`);
    }

    const data = await response.json();

    logPaymentEvent('GET_ORDER_RESPONSE', {
      order_id: data.order_id,
      order_status: data.order_status
    });

    return data;
  } catch (error) {
    logPaymentEvent('GET_ORDER_ERROR', {
      error: error instanceof Error ? error.message : 'Unknown error',
      order_id: orderId
    });

    // In production, rethrow the error
    throw error;
  }
};

/**
 * Verify payment status with Cashfree
 * @param orderId Order ID to verify
 * @param referenceId Reference ID of the payment
 * @returns Payment verification result
 */
export const verifyPaymentStatus = async (orderId: string, referenceId: string) => {
  try {
    logPaymentEvent('VERIFY_PAYMENT_REQUEST', {
      order_id: orderId,
      reference_id: referenceId
    });

    // Get order details first
    const orderDetails = await getOrderDetails(orderId);

    // Check if the order has any payments
    if (!orderDetails.payments || orderDetails.payments.length === 0) {
      throw new Error('No payments found for this order');
    }

    // Find the payment with the matching reference ID
    const payment = orderDetails.payments.find(
      (p: any) => p.cf_payment_id?.toString() === referenceId || p.payment_id === referenceId
    );

    if (!payment) {
      throw new Error('Payment not found for this reference ID');
    }

    logPaymentEvent('VERIFY_PAYMENT_RESPONSE', {
      order_id: orderId,
      reference_id: referenceId,
      payment_status: payment.payment_status
    });

    return {
      success: payment.payment_status === 'SUCCESS',
      status: payment.payment_status,
      amount: payment.payment_amount,
      mode: payment.payment_method?.payment_method_type || 'unknown',
      time: payment.payment_completion_time,
      reference: payment.cf_payment_id || payment.payment_id
    };
  } catch (error) {
    logPaymentEvent('VERIFY_PAYMENT_ERROR', {
      error: error instanceof Error ? error.message : 'Unknown error',
      order_id: orderId,
      reference_id: referenceId
    });

    // In production, rethrow the error
    throw error;
  }
};
