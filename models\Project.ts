import mongoose, { Schema, Document } from 'mongoose';

export interface IProject extends Document {
  name: string;
  description?: string;
  userId: mongoose.Schema.Types.ObjectId;
  website?: string;
  niche: string;
  keywordCount: number;
  clusterCount: number;
  createdAt: Date;
  updatedAt: Date;
}

const ProjectSchema: Schema = new Schema(
  {
    name: {
      type: String,
      required: [true, 'Please provide a project name'],
      maxlength: [100, 'Project name cannot be more than 100 characters'],
    },
    description: {
      type: String,
      maxlength: [500, 'Description cannot be more than 500 characters'],
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    website: {
      type: String,
      trim: true,
    },
    niche: {
      type: String,
      required: [true, 'Niche/domain is required'],
      maxlength: [50, 'Niche/domain cannot be more than 50 characters'],
    },
    keywordCount: {
      type: Number,
      default: 0,
    },
    clusterCount: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.models.Project || mongoose.model<IProject>('Project', ProjectSchema);
