import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { saveOTP } from '../otp-actions';
import { sendOTPToWebhook } from '@/utils/webhookUtils';

export async function POST(request: NextRequest) {
  try {
    const { name, email, plan = 'free' } = await request.json();

    // Validate input
    if (!name || !email) {
      return NextResponse.json(
        { success: false, message: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Validate plan
    if (!['free', 'standard', 'pro'].includes(plan)) {
      return NextResponse.json(
        { success: false, message: 'Invalid plan selected' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Check if user already exists
    const existingUser = await User.findOne({ email });

    // If user exists, just update the name and plan
    if (existingUser) {
      existingUser.name = name;
      existingUser.plan = plan;
      // Set payment status based on plan
      existingUser.paymentStatus = plan === 'free' ? 'completed' : 'pending';
      await existingUser.save();
    } else {
      // Create new user
      await User.create({
        name,
        email,
        plan,
        paymentStatus: plan === 'free' ? 'completed' : 'pending',
      });
    }

    // Generate and send OTP
    const otp = await saveOTP(email);

    try {
      // Send OTP via webhook
      const webhookResponse = await sendOTPToWebhook({
        name,
        email,
        otp,
        route: 'signup',
        plan, // Include the selected plan
        timestamp: Date.now(),
      });

      // Check if the webhook response indicates successful sending
      const isSent = webhookResponse?.isSent === true ||
                     webhookResponse?.success === true ||
                     webhookResponse === true ||
                     webhookResponse === "true" ||
                     (typeof webhookResponse === 'string' && webhookResponse.toLowerCase() === "true") ||
                     (webhookResponse?.message === "Workflow was started") ||
                     (webhookResponse?.message === "OTP would be sent in production environment");

      if (isSent) {
        // Return success response
        return NextResponse.json({
          success: true,
          message: 'User registered and OTP sent successfully',
        });
      } else {
        // Log the webhook response for debugging
        console.warn('Webhook response indicated OTP was not sent:', JSON.stringify(webhookResponse));

        // In production, we'll still return success to avoid blocking users
        if (process.env.NODE_ENV === 'production') {
          console.log('In production environment, returning success response despite webhook failure');
          return NextResponse.json({
            success: true,
            message: 'User registered and OTP sent successfully',
          });
        } else {
          // In development, return the actual error
          return NextResponse.json({
            success: false,
            message: 'User registered but OTP delivery failed. Please try again.',
            webhookResponse,
          }, { status: 400 });
        }
      }
    } catch (webhookError) {
      console.error('Error sending OTP via webhook:', webhookError);

      // In production, we'll still return success to avoid blocking users
      if (process.env.NODE_ENV === 'production') {
        console.log('In production environment, returning success response despite webhook error');
        return NextResponse.json({
          success: true,
          message: 'User registered and OTP sent successfully',
        });
      } else {
        return NextResponse.json({
          success: false,
          message: 'User registered but failed to send OTP. Please try again.',
        }, { status: 500 });
      }
    }
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create user' },
      { status: 500 }
    );
  }
}
