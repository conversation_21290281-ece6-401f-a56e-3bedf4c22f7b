import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Matrix from '@/models/Matrix';
import Project from '@/models/Project';
import { verifyToken } from '@/utils/jwtUtils';
import mongoose from 'mongoose';

export const dynamic = 'force-dynamic';

export async function GET(
  req: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const userId = userData.id;
    const projectId = params.projectId;

    // Validate if projectId is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(projectId)) {
      return NextResponse.json({ error: 'Invalid project ID' }, { status: 400 });
    }

    // Verify the project belongs to the user
    const project = await Project.findOne({
      _id: projectId,
      userId,
    });

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    // Find all matrices for this project
    const matrices = await Matrix.find({
      projectId,
      userId,
    }).sort({ createdAt: -1 });

    return NextResponse.json({ matrices });
  } catch (error) {
    console.error('Error fetching matrices:', error);
    return NextResponse.json({ error: 'Failed to fetch matrices' }, { status: 500 });
  }
}
