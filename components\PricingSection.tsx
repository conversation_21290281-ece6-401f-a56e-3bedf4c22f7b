'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import '@/styles/pricing-carousel.css';

const PricingSection = () => {
  const [annual, setAnnual] = useState(false);
  const [activePlan, setActivePlan] = useState(1); // Default to Standard (index 1)
  const [isMobile, setIsMobile] = useState(false);
  const [isTabSticky, setIsTabSticky] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);
  const pricingSectionRef = useRef<HTMLDivElement>(null);
  const tabsRef = useRef<HTMLDivElement>(null);

  // Check if we're on mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Handle carousel scroll to update active plan
  useEffect(() => {
    const handleScroll = () => {
      if (carouselRef.current && isMobile) {
        const scrollPosition = carouselRef.current.scrollLeft;
        const cardWidth = carouselRef.current.offsetWidth;
        const newActivePlan = Math.round(scrollPosition / cardWidth);

        if (newActivePlan !== activePlan) {
          setActivePlan(newActivePlan);
        }
      }
    };

    const carousel = carouselRef.current;
    if (carousel && isMobile) {
      carousel.addEventListener('scroll', handleScroll);
      return () => carousel.removeEventListener('scroll', handleScroll);
    }
  }, [activePlan, isMobile]);

  // Throttle function to limit how often a function can be called
  const throttle = (func: Function, limit: number) => {
    let inThrottle: boolean;
    return function(this: any, ...args: any[]) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  };

  // Handle sticky tabs on page scroll
  useEffect(() => {
    const handlePageScroll = () => {
      if (!pricingSectionRef.current || !tabsRef.current || !isMobile) return;

      const pricingSectionRect = pricingSectionRef.current.getBoundingClientRect();
      const tabsRect = tabsRef.current.getBoundingClientRect();
      const tabsHeight = tabsRect.height;

      // Get the header height (if there's a fixed header)
      const headerOffset = 0; // Set this to your header height if you have a fixed header

      // Check if pricing section is in viewport
      if (pricingSectionRect.top <= headerOffset && pricingSectionRect.bottom >= (tabsHeight + headerOffset)) {
        // Pricing section is visible and there's enough space for the tabs
        setIsTabSticky(true);
      } else {
        setIsTabSticky(false);
      }
    };

    // Throttle scroll event to improve performance
    const throttledScroll = throttle(handlePageScroll, 100);

    window.addEventListener('scroll', throttledScroll);
    // Run once on mount to check initial position
    handlePageScroll();

    return () => window.removeEventListener('scroll', throttledScroll);
  }, [isMobile]);

  const toggleBilling = () => {
    setAnnual(!annual);
  };

  // Function to navigate to a specific plan in the carousel
  const goToPlan = (index: number) => {
    setActivePlan(index);
    if (carouselRef.current) {
      const scrollAmount = index * carouselRef.current.offsetWidth;
      carouselRef.current.scrollTo({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  const plans = [
    {
      name: 'Free',
      description: 'For beginners and casual users',
      monthlyPrice: 0,
      annualPrice: 0,
      features: [
        '10 keyword searches per month',
        'Basic keyword suggestions (volume, CPC, difficulty)',
        'One complete content cluster',
        'No saved projects or history',
        'No support',
        'No access to AI writing or content briefs',
        'No export/download option',
        'Usage resets monthly',
      ],
      cta: 'Start for Free',
      highlighted: false,
      color: 'gray',
    },
    {
      name: 'Standard',
      description: 'For active creators, bloggers, and small businesses',
      monthlyPrice: 20,
      annualPrice: 15 * 12,
      features: [
        'Up to 150 keyword searches/month',
        'AI-driven topic clustering & content matrix',
        '300+ keyword suggestions with competitive analysis',
        '25 saved projects',
        'Generate up to 3000+ optimized content titles/month',
        'Keyword grouping by intent or topical relevance',
        'Export to CSV',
        'Email support',
      ],
      cta: 'Start with Standard',
      highlighted: true,
      color: 'blue',
    },
    {
      name: 'Pro',
      description: 'For agencies, content teams, and power users',
      monthlyPrice: 50,
      annualPrice: 35 * 12,
      features: [
        'Unlimited keyword searches and clustering',
        'Unlimited optimized content titles/month',
        'Unlimited projects',
        'Bulk export',
        'Priority email support',
        'All Standard Plan features',
      ],
      cta: 'Start with Pro',
      highlighted: false,
      color: 'red',
    },
  ];

  return (
    <div id="pricing" className="py-12 bg-white scroll-mt-12" ref={pricingSectionRef}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div>
          {/* Uppercase label - centered on all devices */}
          <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase text-center">Pricing</h2>
          {/* Main heading - centered on all devices */}
          <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl text-center">
            Choose the right plan for your needs
          </p>
          {/* Subheading - left-aligned on mobile, centered on lg screens */}
          <p className="mt-4 max-w-2xl text-base sm:text-xl text-gray-500 text-left lg:text-center lg:mx-auto">
            From free tools for beginners to powerful solutions for agencies and content teams.
          </p>
        </div>

        {/* Billing toggle - desktop */}
        <div className={`${isMobile ? 'hidden' : 'flex'} mt-8 sm:mt-12 justify-center`}>
          <div className="relative self-center rounded-lg bg-gray-100 p-0.5 flex">
            <button
              type="button"
              className={`${
                !annual ? 'bg-white border-gray-200 shadow-sm text-gray-900' : 'border border-transparent text-gray-700'
              } relative py-2 px-4 sm:px-6 border rounded-md text-sm font-medium whitespace-nowrap focus:outline-none focus:z-10 sm:w-auto sm:px-8`}
              onClick={toggleBilling}
            >
              Monthly
            </button>
            <button
              type="button"
              className={`${
                annual ? 'bg-white border-gray-200 shadow-sm text-gray-900' : 'border border-transparent text-gray-700'
              } ml-0.5 relative py-2 px-4 sm:px-6 border rounded-md text-sm font-medium whitespace-nowrap focus:outline-none focus:z-10 sm:w-auto sm:px-8`}
              onClick={toggleBilling}
            >
              Annual
            </button>
          </div>
        </div>

        {/* Sticky container for mobile tabs */}
        <div
          className={`${isMobile ? 'block' : 'hidden'} ${
            isTabSticky ? 'fixed top-0 left-0 right-0 z-50 bg-white shadow-md py-3 px-4 transition-all duration-200' : 'mt-8'
          }`}
          ref={tabsRef}
        >
          <div className="bg-indigo-50 rounded-xl p-2 flex justify-between max-w-md mx-auto">
            <button
              type="button"
              className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                !annual ? 'bg-white text-indigo-700 shadow-sm' : 'text-indigo-600'
              }`}
              onClick={toggleBilling}
            >
              MONTHLY
            </button>
            <button
              type="button"
              className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                annual ? 'bg-white text-indigo-700 shadow-sm' : 'text-indigo-600'
              }`}
              onClick={toggleBilling}
            >
              ANNUAL
            </button>
          </div>
        </div>

        {/* Spacer for when tabs are sticky */}
        {isMobile && isTabSticky && (
          <div className="h-16"></div>
        )}

        {/* Mobile Pricing Cards Carousel */}
        <div className={`${isMobile ? 'block' : 'hidden'} mt-8`}>

          {/* Carousel container */}
          <div
            ref={carouselRef}
            className="flex overflow-x-auto snap-x snap-mandatory -mx-4 px-4 pb-6 scroll-smooth"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              WebkitOverflowScrolling: 'touch'
            }}
          >
            {plans.map((plan, index) => (
              <div
                key={`mobile-${plan.name}`}
                className="flex-shrink-0 w-full snap-center px-2"
                style={{ scrollSnapAlign: 'center' }}
              >
                <div
                  className={`
                    rounded-xl shadow-md overflow-hidden border h-full flex flex-col
                    ${plan.highlighted ? 'border-indigo-500 border-2' : 'border-gray-200'}
                    ${activePlan === index ? 'ring-2 ring-indigo-300' : ''}
                  `}
                >
                  {/* Plan header */}
                  <div className={`
                    p-4
                    ${plan.highlighted ? 'bg-indigo-500 text-white' : 'bg-white text-gray-900'}
                  `}>
                    <div className="flex justify-between items-center">
                      <h3 className={`text-lg font-bold ${plan.highlighted ? 'text-white' : 'text-gray-900'}`}>
                        {plan.name}
                      </h3>
                      {plan.highlighted && (
                        <span className="text-xs font-medium bg-white text-indigo-600 px-2 py-1 rounded-full">
                          Most Popular
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Price */}
                  <div className="bg-white p-4 flex-grow">
                    <div className="flex items-baseline">
                      <span className="text-3xl font-extrabold text-gray-900">
                        {plan.monthlyPrice === 0 ? 'Free' : `$${annual ? Math.round(plan.annualPrice / 12) : plan.monthlyPrice}`}
                      </span>
                      {plan.monthlyPrice !== 0 && (
                        <span className="ml-1 text-sm font-medium text-gray-500">
                          /month
                        </span>
                      )}
                    </div>

                    {annual && plan.monthlyPrice !== 0 && (
                      <div className="mt-1 flex items-center">
                        <span className="text-xs bg-green-50 text-green-700 px-1.5 py-0.5 rounded-sm font-medium">
                          {plan.name === 'Standard' ? 'SAVE 25%' : 'SAVE 30%'}
                        </span>
                        <span className="ml-2 text-xs text-gray-500">
                          Billed annually
                        </span>
                      </div>
                    )}

                    <p className="mt-3 text-sm text-gray-500">
                      {plan.description}
                    </p>

                    {/* Key features */}
                    <div className="mt-4">
                      <div>
                        <h4 className="text-xs font-medium text-gray-900 uppercase tracking-wide">Key features</h4>
                      </div>
                      <ul className="mt-2 space-y-2">
                        {plan.features.map((feature) => (
                          <li key={feature} className="flex items-start">
                            <svg
                              className="flex-shrink-0 h-4 w-4 text-green-500 mt-0.5"
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span className="ml-2 text-xs text-gray-500">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* CTA Button */}
                    <Link
                      href="/app"
                      className={`
                        mt-6 block w-full py-3 px-4 rounded-lg text-center text-sm font-medium
                        ${
                          plan.highlighted
                            ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                            : plan.name === 'Free'
                            ? 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                            : plan.color === 'red'
                            ? 'bg-red-50 text-red-700 hover:bg-red-100 border border-red-200'
                            : 'bg-indigo-50 text-indigo-700 hover:bg-indigo-100 border border-indigo-200'
                        }
                      `}
                    >
                      {plan.cta}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Carousel indicators */}
          <div className="flex justify-center mt-4 space-x-2">
            {plans.map((_, index) => (
              <button
                key={`indicator-${index}`}
                className={`w-2 h-2 rounded-full transition-colors ${
                  activePlan === index ? 'bg-indigo-600' : 'bg-gray-300'
                }`}
                onClick={() => goToPlan(index)}
                aria-label={`Go to plan ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Desktop Pricing Cards Grid */}
        <div className={`${isMobile ? 'hidden' : 'block'} mt-8 sm:mt-12 space-y-6 sm:space-y-0 sm:grid sm:grid-cols-1 md:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:mx-0 xl:grid-cols-3`}>
          {plans.map((plan) => (
            <div
              key={plan.name}
              className={`${
                plan.highlighted
                  ? 'border-indigo-500 border-2'
                  : 'border-gray-200'
              } rounded-lg shadow-sm divide-y divide-gray-200 flex flex-col h-full`}
            >
              {plan.highlighted && (
                <div className="bg-indigo-500 text-white text-center py-2 px-4 rounded-t-lg">
                  Most Popular
                </div>
              )}
              {plan.color === 'blue' && (
                <div className="absolute top-0 right-0 h-8 w-8 bg-blue-500 rounded-full transform translate-x-1/2 -translate-y-1/2 flex items-center justify-center">
                  <span className="text-white text-xs font-bold">🔵</span>
                </div>
              )}
              {plan.color === 'red' && (
                <div className="absolute top-0 right-0 h-8 w-8 bg-red-500 rounded-full transform translate-x-1/2 -translate-y-1/2 flex items-center justify-center">
                  <span className="text-white text-xs font-bold">🔴</span>
                </div>
              )}
              <div className={`p-4 sm:p-6 ${plan.highlighted && !plan.highlighted ? 'rounded-t-lg' : ''} flex-shrink-0`}>
                <h2 className="text-lg leading-6 font-medium text-gray-900">{plan.name}</h2>
                <p className="mt-3 sm:mt-4 text-sm text-gray-500">{plan.description}</p>
                <p className="mt-6 sm:mt-8">
                  <span className="text-3xl sm:text-4xl font-extrabold text-gray-900">
                    {plan.monthlyPrice === 0 ? 'Free' : `$${annual ? Math.round(plan.annualPrice / 12) : plan.monthlyPrice}`}
                  </span>
                  {plan.monthlyPrice !== 0 && (
                    <span className="text-sm sm:text-base font-medium text-gray-500">
                      /month
                    </span>
                  )}
                </p>
                {annual && plan.monthlyPrice !== 0 && (
                  <div className="mt-2 flex items-center">
                    <span className="text-sm bg-green-50 text-green-700 px-2 py-0.5 rounded-sm font-medium">
                      {plan.name === 'Standard' ? 'SAVE 25%' : 'SAVE 30%'}
                    </span>
                    <span className="ml-2 text-sm text-gray-500">
                      Billed annually (${plan.annualPrice}/year)
                    </span>
                  </div>
                )}
                <Link
                  href="/app"
                  className={`${
                    plan.highlighted
                      ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                      : plan.name === 'Free'
                      ? 'bg-gray-100 text-gray-700 hover:bg-gray-200 border-gray-300'
                      : plan.color === 'red'
                      ? 'bg-red-50 text-red-700 hover:bg-red-100'
                      : 'bg-indigo-50 text-indigo-700 hover:bg-indigo-100'
                  } mt-6 sm:mt-8 block w-full py-2 sm:py-3 px-4 sm:px-6 border border-transparent rounded-md text-center font-medium text-sm sm:text-base`}
                >
                  {plan.cta}
                </Link>
              </div>
              <div className="pt-4 sm:pt-6 pb-6 sm:pb-8 px-4 sm:px-6 flex-grow">
                <h3 className="text-xs font-medium text-gray-900 tracking-wide uppercase">What's included</h3>
                <ul className="mt-4 sm:mt-6 space-y-3 sm:space-y-4">
                  {plan.features.map((feature) => {
                    // Check if feature starts with an emoji
                    const hasEmoji = /^[🔍📊✏️📁💬🚫❌📆✅🧠📈📝🧩📤🚨]/.test(feature);
                    const featureText = hasEmoji ? feature : feature;

                    return (
                      <li key={feature} className="flex space-x-3">
                        {!hasEmoji && (
                          <svg
                            className="flex-shrink-0 h-5 w-5 text-green-500"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden="true"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        )}
                        <span className="text-sm text-gray-500">{featureText}</span>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PricingSection;
