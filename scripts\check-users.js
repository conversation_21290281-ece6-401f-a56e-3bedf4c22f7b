// Script to check users and OTPs in the database
const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI;

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');

    // Define User schema
    const UserSchema = new mongoose.Schema({
      name: String,
      email: String,
      createdAt: Date,
      updatedAt: Date
    });

    // Define OTP schema
    const OTPSchema = new mongoose.Schema({
      email: String,
      otp: String,
      createdAt: Date,
      expiresAt: Date
    });

    // Create User model (or get it if it exists)
    const User = mongoose.models.User || mongoose.model('User', UserSchema);

    // Create OTP model (or get it if it exists)
    const OTP = mongoose.models.OTP || mongoose.model('OTP', OTPSchema);

    // Find all users
    return User.find({}).then(users => {
      console.log('Users in database:');
      console.log(JSON.stringify(users, null, 2));

      // Find all OTPs
      return OTP.find({}).then(otps => {
        console.log('\nOTPs in database:');
        console.log(JSON.stringify(otps, null, 2));
      });
    });
  })
  .then(() => {
    console.log('Done');
    process.exit(0);
  })
  .catch(err => {
    console.error('Error:', err);
    process.exit(1);
  });
