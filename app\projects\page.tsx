'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import ProjectList from '@/components/ProjectList';
import CreateProjectModal from '@/components/CreateProjectModal';
import { Project } from '@/types';
import { useAuth } from '@/context/AuthContext';

export default function ProjectsPage() {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Modal is now handled by the ProjectList component

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login-with-otp');
    }
  }, [user, authLoading, router]);

  // Fetch user's projects
  useEffect(() => {
    const fetchProjects = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        // Fetch projects from the API
        const response = await fetch('/api/projects', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // Include cookies for authentication
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch projects');
        }

        const data = await response.json();

        // Map the MongoDB documents to our Project type
        const fetchedProjects: Project[] = data.projects.map((project: any) => ({
          id: project._id,
          name: project.name,
          description: project.description || '',
          userId: project.userId,
          website: project.website || '',
          niche: project.niche,
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
        }));

        setProjects(fetchedProjects);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError('Failed to load your projects. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
  }, [user]);

  // Handle project creation
  const handleCreateProject = async (name: string, description: string, website: string, niche: string) => {
    if (!user) return;

    try {
      // Send project data to the API
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify({
          name,
          description,
          website,
          niche,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create project');
      }

      const data = await response.json();

      // Create a Project object from the API response
      const newProject: Project = {
        id: data.project._id,
        name: data.project.name,
        description: data.project.description || '',
        userId: data.project.userId,
        website: data.project.website || '',
        niche: data.project.niche,
        createdAt: data.project.createdAt,
        updatedAt: data.project.updatedAt,
      };

      // Add the new project to the state
      setProjects(prevProjects => [...prevProjects, newProject]);

      // Redirect to the matrix page with the project ID as a query parameter
      router.push(`/matrix?projectId=${data.project._id}`);

    } catch (err) {
      console.error('Error creating project:', err);
      throw new Error('Failed to create project. Please try again.');
    }
  };

  // No longer needed as we're using the modal directly in the ProjectList component

  return (
    <AppLayout>
      <div className="space-y-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Projects</h1>
          <p className="text-gray-600">
            Manage your projects and create SEO content matrices for each one.
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/4"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        ) : (
          <ProjectList
            projects={projects}
            onCreateProject={handleCreateProject}
          />
        )}
      </div>

      {/* This modal is now handled by the ProjectList component */}
    </AppLayout>
  );
}
