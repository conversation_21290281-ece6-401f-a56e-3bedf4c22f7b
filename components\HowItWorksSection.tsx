import Image from 'next/image';

const HowItWorksSection = () => {
  const steps = [
    {
      id: 1,
      title: 'Enter your main keyword',
      description: 'Start by entering your primary keyword, target location, and preferred language.',
      icon: (
        <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
    },
    {
      id: 2,
      title: 'Generate keyword research',
      description: 'Our system analyzes your input and generates comprehensive keyword research with detailed metrics.',
      icon: (
        <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
    },
    {
      id: 3,
      title: 'Review content matrix',
      description: 'Explore the generated content matrix with content types, focus keywords, and categories.',
      icon: (
        <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      ),
    },
    {
      id: 4,
      title: 'Plan and create content',
      description: 'Use the insights to plan and create SEO-optimized content that ranks higher in search results.',
      icon: (
        <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      ),
    },
  ];

  return (
    <div id="how-it-works" className="py-10 sm:py-12 bg-gray-50 scroll-mt-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div>
          {/* Uppercase label - centered on all devices */}
          <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase text-center">How It Works</h2>
          {/* Main heading - centered on all devices */}
          <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl text-center">
            Simple process, powerful results
          </p>
          {/* Subheading - left-aligned on mobile, centered on larger screens */}
          <p className="mt-3 sm:mt-4 max-w-2xl text-base sm:text-xl text-gray-500 text-left lg:text-center mx-auto">
            Our streamlined workflow helps you go from keyword to content plan in minutes, not hours.
          </p>
        </div>

        <div className="mt-8 sm:mt-10">
          <div className="relative">
            {/* Process steps - vertical timeline for both mobile and desktop */}
            <div className="absolute left-8 sm:left-12 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full bg-indigo-200"></div>

            <div className="space-y-12 sm:space-y-16">
              {steps.map((step, index) => (
                <div key={step.id} className={`relative ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} md:flex`}>
                  <div className="md:w-1/2"></div>

                  {/* Step circle - visible on all screen sizes */}
                  <div className="absolute left-8 sm:left-12 md:left-1/2 transform md:-translate-x-1/2 -translate-y-4 flex items-center justify-center z-10">
                    <div className="flex items-center justify-center h-10 w-10 sm:h-12 sm:w-12 rounded-full bg-indigo-500 text-white shadow-lg transition-all duration-300 hover:scale-110">
                      <span className="text-lg font-semibold">{step.id}</span>
                    </div>
                  </div>

                  {/* Horizontal connector line for mobile */}
                  <div className="absolute left-[33px] sm:left-[45px] top-0 w-[16px] border-t-2 border-indigo-200 md:hidden"></div>

                  {/* Step content */}
                  <div className={`relative pl-16 sm:pl-24 md:pl-0 md:w-1/2 ${index % 2 === 0 ? 'md:ml-12' : 'md:mr-12'}`}>
                    <div className="bg-white p-5 sm:p-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">{step.title}</h3>
                      <p className="mt-2 text-base text-gray-500">{step.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HowItWorksSection;
