'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import { useAuth } from '@/context/AuthContext';

export default function UpgradePlanPage() {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [selectedPlan, setSelectedPlan] = useState<'standard' | 'pro'>('standard');
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');
  const [isLoading, setIsLoading] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login-with-otp');
    }
  }, [user, authLoading, router]);

  const handleUpgrade = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      // In a real app, you would call your payment API
      // For demo purposes, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Redirect to a payment page or show success
      router.push(`/payment/checkout?plan=${selectedPlan}&cycle=${billingCycle}`);
    } catch (err) {
      console.error('Error upgrading plan:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const plans = [
    {
      name: 'Standard',
      id: 'standard',
      description: 'Perfect for small blogs and websites',
      monthlyPrice: 20,
      annualPrice: 15 * 12, // $15/month billed annually
      features: [
        'Up to 10 projects',
        '100 keyword searches per month',
        'Content matrix generation',
        'Export to CSV and JSON',
        'Email support'
      ]
    },
    {
      name: 'Pro',
      id: 'pro',
      description: 'For professional content marketers',
      monthlyPrice: 49,
      annualPrice: 39 * 12, // $39/month billed annually
      features: [
        'Unlimited projects',
        'Unlimited keyword searches',
        'Advanced content matrix generation',
        'Export to all formats',
        'Priority support',
        'API access',
        'Team collaboration'
      ]
    }
  ];

  return (
    <AppLayout>
      <div className="space-y-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Upgrade Your Plan</h1>
          <p className="text-gray-600">
            Choose the plan that best fits your needs and take your content strategy to the next level.
          </p>
        </div>

        {/* Billing Cycle Toggle */}
        <div className="flex justify-center">
          <div className="bg-gray-100 p-1 rounded-lg inline-flex">
            <button
              onClick={() => setBillingCycle('monthly')}
              className={`px-4 py-2 text-sm font-medium rounded-md ${
                billingCycle === 'monthly'
                  ? 'bg-white shadow-sm text-indigo-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('annual')}
              className={`px-4 py-2 text-sm font-medium rounded-md ${
                billingCycle === 'annual'
                  ? 'bg-white shadow-sm text-indigo-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Annual <span className="text-green-500 text-xs font-semibold">Save 20%</span>
            </button>
          </div>
        </div>

        {/* Plans */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {plans.map((plan) => {
            const isSelected = selectedPlan === plan.id;
            const price = billingCycle === 'monthly' ? plan.monthlyPrice : plan.annualPrice;
            const pricePerMonth = billingCycle === 'monthly' ? price : price / 12;
            
            return (
              <div 
                key={plan.id}
                className={`bg-white rounded-lg shadow-md overflow-hidden border-2 transition-all ${
                  isSelected ? 'border-indigo-500 ring-2 ring-indigo-200' : 'border-transparent'
                }`}
                onClick={() => setSelectedPlan(plan.id as 'standard' | 'pro')}
              >
                <div className="p-6">
                  <h2 className="text-xl font-bold text-gray-900">{plan.name}</h2>
                  <p className="text-gray-500 mt-1">{plan.description}</p>
                  
                  <div className="mt-4">
                    <span className="text-3xl font-bold text-gray-900">${pricePerMonth}</span>
                    <span className="text-gray-500 ml-1">/month</span>
                    
                    {billingCycle === 'annual' && (
                      <div className="text-green-600 text-sm font-medium mt-1">
                        Billed annually (${price}/year)
                      </div>
                    )}
                  </div>
                  
                  <ul className="mt-6 space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <button
                    onClick={() => {
                      setSelectedPlan(plan.id as 'standard' | 'pro');
                      handleUpgrade();
                    }}
                    disabled={isLoading}
                    className={`mt-8 w-full py-3 px-4 rounded-md font-medium text-white ${
                      isSelected
                        ? 'bg-indigo-600 hover:bg-indigo-700'
                        : 'bg-gray-600 hover:bg-gray-700'
                    } transition-colors ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                  >
                    {isLoading && isSelected ? 'Processing...' : `Upgrade to ${plan.name}`}
                  </button>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="bg-gray-50 rounded-lg p-4 text-center text-sm text-gray-500">
          All plans include a 14-day money-back guarantee. No questions asked.
        </div>
      </div>
    </AppLayout>
  );
}
