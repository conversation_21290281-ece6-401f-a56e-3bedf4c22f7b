'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import ProjectSelectionModal from '@/components/ProjectSelectionModal';
import { useAuth } from '@/context/AuthContext';

export default function AppMatrixPage() {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [isProjectSelectionModalOpen, setIsProjectSelectionModalOpen] = useState(true);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login-with-otp');
    }
  }, [user, authLoading, router]);

  return (
    <AppLayout>
      <ProjectSelectionModal
        isOpen={isProjectSelectionModalOpen}
        onClose={() => setIsProjectSelectionModalOpen(false)}
      />

      <div className="space-y-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Matrix Generator</h1>
          <p className="text-gray-600">
            Create and manage your SEO content matrices. Select a project to get started.
          </p>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Select a Project</h2>
          <p className="text-gray-600 mb-6">
            Please select a project from the Projects section or create a new project to use the Matrix Generator.
          </p>
          <button
            onClick={() => setIsProjectSelectionModalOpen(true)}
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Select Project
          </button>
        </div>
      </div>
    </AppLayout>
  );
}
