'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';

// Component that uses searchParams
function PaymentVerification() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error'>('pending');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const verifyPayment = async () => {
      try {
        if (!searchParams) {
          console.error('Search params not available');
          setVerificationStatus('error');
          setErrorMessage('Payment verification failed: Missing parameters');
          return;
        }

        // Get orderId from URL parameters
        const orderId = searchParams.get('orderId');

        if (!orderId) {
          console.error('No orderId found in URL parameters');
          setVerificationStatus('error');
          setErrorMessage('Payment verification failed: Missing order ID');
          return;
        }

        // Get payment details from Cashfree callback parameters
        const orderIdFromCashfree = searchParams.get('order_id') || orderId;
        const transactionId = searchParams.get('transaction_id') ||
                             searchParams.get('cf_txn_id') ||
                             searchParams.get('referenceId') ||
                             'verify_' + Date.now(); // Generate a verification ID if none provided
        const paymentStatus = searchParams.get('txStatus') || 'SUCCESS'; // Assume success if not provided

        console.log('Verifying payment with details:', {
          orderId: orderIdFromCashfree,
          transactionId,
          paymentStatus
        });

        // Verify payment with our API - this will check the order status with Cashfree
        const response = await fetch('/api/payment/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            order_id: orderIdFromCashfree,
            transaction_id: transactionId,
            transaction_status: paymentStatus,
          }),
        });

        const data = await response.json();

        console.log('Payment verification response:', data);

        if (data.success) {
          setVerificationStatus('success');
          console.log('Payment verified successfully:', {
            user: data.user,
            plan: data.user?.plan,
            paymentStatus: data.user?.paymentStatus
          });

          // Redirect to dashboard after 5 seconds
          const timer = setTimeout(() => {
            router.push('/dashboard');
          }, 5000);

          return () => clearTimeout(timer);
        } else {
          setVerificationStatus('error');
          setErrorMessage(data.message || 'Payment verification failed');
          console.error('Payment verification failed:', data.message);
        }
      } catch (err) {
        console.error('Error verifying payment:', err);
        setVerificationStatus('error');
        setErrorMessage('An error occurred while verifying your payment');
      }
    };

    verifyPayment();
  }, [router, searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-10 rounded-lg shadow-md">
        {verificationStatus === 'pending' && (
          <div>
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100">
              <svg
                className="h-10 w-10 text-yellow-600 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Verifying Payment...
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Please wait while we verify your payment.
            </p>
          </div>
        )}

        {verificationStatus === 'success' && (
          <div>
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100">
              <svg
                className="h-10 w-10 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Payment Successful!
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Thank you for your payment. Your subscription has been activated.
            </p>
            <div className="mt-8 space-y-6">
              <p className="text-center text-gray-500">
                You will be redirected to the app in 5 seconds...
              </p>
              <div className="flex justify-center">
                <Link
                  href="/app"
                  className="group relative flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Go to App Now
                </Link>
              </div>
            </div>
          </div>
        )}

        {verificationStatus === 'error' && (
          <div>
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100">
              <svg
                className="h-10 w-10 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Payment Verification Failed
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              {errorMessage || "We couldn't verify your payment. Please contact support."}
            </p>
            <div className="mt-8 space-y-6">
              <div className="flex justify-center">
                <Link
                  href="/app"
                  className="group relative flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Go to App
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Loading fallback
function PaymentSuccessLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-10 rounded-lg shadow-md">
        <div>
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100">
            <svg
              className="h-10 w-10 text-yellow-600 animate-spin"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Processing Payment...
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Please wait while we process your payment.
          </p>
        </div>
      </div>
    </div>
  );
}

// Main component with Suspense boundary
export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<PaymentSuccessLoading />}>
      <PaymentVerification />
    </Suspense>
  );
}
