'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import CashfreePayment from '../payment/CashfreePayment';
import { formatEmailForDisplay } from '@/utils/otpUtils';

interface OTPVerificationFormProps {
  email: string;
  onBack: () => void;
  authFlow?: 'signup' | 'signin';
}

const OTPVerificationForm = ({ email, onBack, authFlow = 'signin' }: OTPVerificationFormProps) => {
  const router = useRouter();
  const { setUser } = useAuth();
  const [otp, setOtp] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds
  const [canResend, setCanResend] = useState(false);
  const [verificationComplete, setVerificationComplete] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [verifiedUser, setVerifiedUser] = useState<any>(null);

  // Timer for OTP expiration
  useEffect(() => {
    if (timeLeft <= 0) {
      setCanResend(true);
      return;
    }

    const timer = setTimeout(() => {
      setTimeLeft(timeLeft - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [timeLeft]);

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-flow': authFlow, // Pass the auth flow type
        },
        body: JSON.stringify({ email, otp }),
      });

      const data = await response.json();

      if (data.success && data.user) {
        // Store the verified user
        setVerifiedUser(data.user);

        // Debug logs
        console.log('OTP verification successful:', data.user);
        console.log('Auth flow:', authFlow);
        console.log('User plan:', data.user.plan);
        console.log('Should show payment?', authFlow === 'signup' && (data.user.plan === 'standard' || data.user.plan === 'pro'));

        // Store the JWT token in a cookie
        if (data.token) {
          document.cookie = `auth_token=${data.token}; path=/; max-age=${60 * 60 * 24 * 30}`; // 30 days
        }

        // Check if this is a signup flow and if the plan is paid
        if (authFlow === 'signup' && (data.user.plan === 'standard' || data.user.plan === 'pro')) {
          // Show payment form for paid plans
          console.log('Showing payment form');
          setShowPayment(true);
        } else {
          // For free plans or signin, proceed directly
          console.log('Proceeding directly to app');
          completeAuthentication(data.user);
        }
      } else {
        setError(data.message || 'Invalid OTP. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error('OTP verification error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to complete the authentication process
  const completeAuthentication = async (user: any) => {
    // Set user in auth context
    setUser(user);

    // Store user in localStorage
    localStorage.setItem('user', JSON.stringify(user));

    // Mark verification as complete
    setVerificationComplete(true);

    // Check if user has projects to determine redirect destination
    try {
      const response = await fetch('/api/projects');
      if (response.ok) {
        const data = await response.json();
        const userProjects = data.projects || [];

        // If user has no projects, redirect to onboarding
        if (userProjects.length === 0) {
          router.push('/onboarding');
        } else {
          // If user has projects, redirect to dashboard
          router.push('/dashboard');
        }
      } else {
        // If we can't fetch projects, default to dashboard
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Error checking user projects:', error);
      // If there's an error, default to dashboard
      router.push('/dashboard');
    }
  };

  // Handle payment success
  const handlePaymentSuccess = () => {
    if (verifiedUser) {
      // Update the user's payment status
      const updatedUser = {
        ...verifiedUser,
        paymentStatus: 'completed'
      };

      completeAuthentication(updatedUser);
    }
  };

  // Handle payment cancellation
  const handlePaymentCancel = () => {
    // Go back to the OTP verification form
    setShowPayment(false);
  };

  const handleResendOTP = async () => {
    setError(null);
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        setTimeLeft(600); // Reset timer
        setCanResend(false);
      } else {
        setError(data.message || 'Failed to resend OTP. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
      console.error('Resend OTP error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Determine what to render based on state
  const renderContent = () => {
    if (showPayment && verifiedUser) {
      console.log('Rendering CashfreePayment component');
      return (
        <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Payment Page</h2>
          <CashfreePayment
            user={verifiedUser}
            onSuccess={handlePaymentSuccess}
            onCancel={handlePaymentCancel}
          />
        </div>
      );
    }

    // Otherwise show OTP verification form
    return (
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Verify OTP</h2>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        <p className="text-green-600 mb-2">
          OTP Sent Successfully to:
        </p>
        <p className="font-medium text-gray-800 mb-6">
          {formatEmailForDisplay(email)}
        </p>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
              Enter OTP
            </label>
            <input
              id="otp"
              name="otp"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={6}
              autoComplete="one-time-code"
              required
              value={otp}
              onChange={(e) => setOtp(e.target.value.replace(/\D/g, ''))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-center text-lg tracking-widest"
              placeholder="------"
            />
            <p className="mt-2 text-sm text-gray-500 text-center">
              {canResend ? (
                <button
                  type="button"
                  onClick={handleResendOTP}
                  disabled={isSubmitting}
                  className="text-indigo-600 hover:text-indigo-500 font-medium"
                >
                  Resend OTP
                </button>
              ) : (
                <>OTP expires in <span className="font-medium">{formatTime(timeLeft)}</span></>
              )}
            </p>
          </div>

          <div>
            <button
              type="submit"
              disabled={isSubmitting || otp.length !== 6}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {isSubmitting ? 'Verifying...' : 'Verify OTP'}
            </button>
          </div>
        </form>

        <div className="mt-6">
          <button
            type="button"
            onClick={onBack}
            disabled={isSubmitting}
            className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Email Entry
          </button>
        </div>
      </div>
    );
  };

  // Render the appropriate content based on state
  return renderContent();
};

export default OTPVerificationForm;
