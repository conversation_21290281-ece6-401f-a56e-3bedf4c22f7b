/**
 * Cashfree Payment Gateway Utilities
 *
 * This file contains utility functions for integrating with the Cashfree payment gateway.
 */

// Types for Cashfree API
export interface CashfreeOrderRequest {
  order_id: string;
  order_amount: number;
  order_currency: string;
  customer_details: {
    customer_id: string;
    customer_name: string;
    customer_email: string;
    customer_phone?: string;
  };
  order_meta?: {
    return_url: string;
    notify_url?: string;
    payment_methods?: string;
  };
  order_note?: string;
}

export interface CashfreeOrderResponse {
  cf_order_id: number;
  order_id: string;
  entity: string;
  order_currency: string;
  order_amount: number;
  order_status: string;
  payment_session_id: string;
  order_expiry_time: string;
  order_note?: string;
  payments?: any;
  refunds?: any;
  settlements?: any;
  order_token: string; // Added for compatibility with our implementation
}

export interface CashfreePaymentResponse {
  orderId: string;
  orderAmount: number;
  referenceId: string;
  txStatus: 'SUCCESS' | 'FAILED' | 'CANCELLED' | 'PENDING';
  paymentMode: string;
  txMsg: string;
  txTime: string;
  signature: string;
}

/**
 * Create a new order with Cashfree
 * @param orderData Order data to be sent to Cashfree
 * @returns Order response from Cashfree
 */
export const createCashfreeOrder = async (orderData: CashfreeOrderRequest): Promise<CashfreeOrderResponse> => {
  try {
    console.log('Creating Cashfree order with data:', JSON.stringify(orderData, null, 2));

    // Import the SDK implementation
    const { createOrderWithSdk } = await import('./cashfreeSdk');

    // Use the SDK to create the order
    return await createOrderWithSdk(orderData);
  } catch (error) {
    console.error('Error creating Cashfree order:', error);

    // No mock implementation for production

    throw error;
  }
};

/**
 * Verify the payment signature from Cashfree
 * @param orderId Order ID
 * @param orderAmount Order amount
 * @param referenceId Reference ID
 * @param txStatus Transaction status
 * @param paymentMode Payment mode
 * @param txMsg Transaction message
 * @param txTime Transaction time
 * @param signature Signature from Cashfree
 * @returns Boolean indicating if the signature is valid
 */
export const verifyCashfreeSignature = (
  orderId: string,
  orderAmount: string,
  referenceId: string,
  txStatus: string,
  paymentMode: string,
  txMsg: string,
  txTime: string,
  signature: string
): boolean => {
  try {
    // Import configuration
    const { CASHFREE_SECRET_KEY, isProduction, logPaymentEvent } = require('./cashfreeConfig');

    if (!CASHFREE_SECRET_KEY) {
      throw new Error('Cashfree secret key not found in environment variables');
    }

    logPaymentEvent('VERIFY_SIGNATURE', {
      orderId,
      txStatus,
      referenceId: referenceId ? referenceId.substring(0, 8) + '...' : '',
      signature: signature ? signature.substring(0, 8) + '...' : ''
    });

    // Always verify the signature
    const crypto = require('crypto');
    const data = orderId + orderAmount + referenceId + txStatus + paymentMode + txMsg + txTime;
    const computedSignature = crypto.createHmac('sha256', CASHFREE_SECRET_KEY).update(data).digest('base64');

    const isValid = computedSignature === signature;

    if (!isValid) {
      logPaymentEvent('SIGNATURE_VERIFICATION_FAILED', {
        orderId,
        expected: computedSignature ? computedSignature.substring(0, 8) + '...' : '',
        received: signature ? signature.substring(0, 8) + '...' : ''
      });
    }

    return isValid;
  } catch (error) {
    // Import logging function
    const { logPaymentEvent } = require('./cashfreeConfig');

    logPaymentEvent('SIGNATURE_VERIFICATION_ERROR', {
      error: error instanceof Error ? error.message : 'Unknown error',
      orderId
    });

    return false;
  }
};

/**
 * Generate a unique order ID
 * @param userId User ID
 * @returns Unique order ID
 */
export const generateOrderId = (userId: string): string => {
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `ORDER_${userId.substring(0, 6)}_${timestamp}_${randomStr}`.toUpperCase();
};
