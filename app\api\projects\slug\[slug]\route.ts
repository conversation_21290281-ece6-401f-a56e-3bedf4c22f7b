import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Project from '@/models/Project';
import { verifyToken } from '@/utils/jwtUtils';

export const dynamic = 'force-dynamic';

export async function GET(
  req: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Get the auth token from cookies
    const authToken = req.cookies.get('auth_token')?.value;

    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    const userData = verifyToken(authToken);

    if (!userData || !userData.id) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    await dbConnect();

    const userId = userData.id;
    const slug = params.slug;

    // Find all projects for this user
    const projects = await Project.find({ userId });

    // Find the project with a matching slug (name converted to slug format)
    const project = projects.find(p =>
      p.name.toLowerCase().replace(/\s+/g, '-') === slug
    );

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    return NextResponse.json({ project });
  } catch (error) {
    console.error('Error fetching project by slug:', error);
    return NextResponse.json({ error: 'Failed to fetch project' }, { status: 500 });
  }
}
