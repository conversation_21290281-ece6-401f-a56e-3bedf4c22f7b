'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '@/types';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  requestOTP: (email: string) => Promise<{ success: boolean; message: string }>;
  verifyOTP: (email: string, otp: string) => Promise<{ success: boolean; message: string; user?: User }>;
  signUp: (name: string, email: string, plan: string) => Promise<{ success: boolean; message: string }>;
  signOut: () => void;
  setUser: (user: User) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (error) {
        console.error('Failed to parse stored user:', error);
        localStorage.removeItem('user');
      }
    }
    setIsLoading(false);
  }, []);

  const requestOTP = async (email: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        return { success: true, message: 'OTP sent successfully' };
      } else {
        return { success: false, message: data.message || 'Failed to send OTP' };
      }
    } catch (error) {
      console.error('OTP request error:', error);
      return { success: false, message: 'An error occurred while sending OTP' };
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOTP = async (email: string, otp: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp }),
      });

      const data = await response.json();

      if (data.success && data.user) {
        setUser(data.user);

        // Store user in localStorage
        localStorage.setItem('user', JSON.stringify(data.user));

        // Set auth token in cookie for server-side access
        // This will be used by the middleware for authentication
        document.cookie = `auth_token=${data.token}; path=/; max-age=${60 * 60 * 24 * 30}`; // 30 days

        return { success: true, message: 'OTP verified successfully', user: data.user };
      } else {
        return { success: false, message: data.message || 'Invalid OTP' };
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      return { success: false, message: 'An error occurred during OTP verification' };
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (name: string, email: string, plan: string) => {
    setIsLoading(true);
    try {
      // First, call the signup API to create the user
      const signupResponse = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, email, plan }),
      });

      const signupData = await signupResponse.json();

      if (!signupData.success) {
        return { success: false, message: signupData.message || 'Failed to create user' };
      }

      // Return success response with message to verify OTP
      return {
        success: true,
        message: 'OTP sent successfully. Please verify to complete signup.'
      };
    } catch (error) {
      console.error('Sign up error:', error);
      return { success: false, message: 'An error occurred during sign up' };
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = () => {
    setUser(null);

    // Clear user from localStorage
    localStorage.removeItem('user');

    // Clear auth token cookie
    document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';

    // Redirect to home page after logout
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, requestOTP, verifyOTP, signUp, signOut, setUser }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
