/**
 * Utility functions for smooth scrolling
 */

/**
 * Scrolls smoothly to the element with the given ID
 * @param elementId - The ID of the element to scroll to (without the # prefix)
 * @param offset - Optional offset from the top of the element (default: 80px to account for fixed header)
 */
export const scrollToElement = (elementId: string, offset: number = 80): void => {
  // Remove the # if it's included in the elementId
  const id = elementId.startsWith('#') ? elementId.substring(1) : elementId;
  
  // Find the element
  const element = document.getElementById(id);
  
  if (element) {
    // Get the element's position relative to the viewport
    const elementPosition = element.getBoundingClientRect().top;
    
    // Get the current scroll position
    const offsetPosition = elementPosition + window.pageYOffset - offset;
    
    // Scroll to the element
    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }
};

/**
 * Handles click on navigation links with hash
 * @param e - Click event
 * @param hash - The hash to navigate to (with the # prefix)
 * @param callback - Optional callback function to execute after navigation (e.g., close mobile menu)
 */
export const handleNavLinkClick = (
  e: React.MouseEvent<HTMLAnchorElement>, 
  hash: string,
  callback?: () => void
): void => {
  // Only handle hash links on the same page
  if (hash.startsWith('#')) {
    e.preventDefault();
    
    // Extract the element ID from the hash
    const elementId = hash.substring(1);
    
    // Scroll to the element
    scrollToElement(elementId);
    
    // Update URL without causing a page reload
    window.history.pushState({}, '', hash);
    
    // Execute callback if provided (e.g., close mobile menu)
    if (callback) {
      callback();
    }
  }
};
