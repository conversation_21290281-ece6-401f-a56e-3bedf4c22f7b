# Cashfree Payment Gateway Integration

This document provides an overview of the Cashfree payment gateway integration in the SEO Content Matrix application.

## Overview

The application uses Cashfree's payment gateway to process payments for subscription plans. The integration uses the official Cashfree Node.js SDK (`cashfree-pg`) and the JavaScript SDK (`@cashfreepayments/cashfree-js`) for client-side integration.

## Environment Variables

The following environment variables are required for the Cashfree integration:

```
# Cashfree Payment Gateway Credentials
CASHFREE_APP_ID="YOUR_CASHFREE_APP_ID"
CASHFREE_SECRET_KEY="YOUR_CASHFREE_SECRET_KEY"
CASHFREE_API_URL="https://sandbox.cashfree.com/pg" # Use https://api.cashfree.com/pg for production
NEXT_PUBLIC_CASHFREE_JS_URL="https://sdk.cashfree.com/js/v3/cashfree.js"
NEXT_PUBLIC_BASE_URL="http://localhost:3001" # Your application's base URL
```

## Implementation Files

The Cashfree integration is implemented in the following files:

1. `utils/cashfreeSdk.ts` - SDK implementation for server-side integration
2. `utils/cashfreeUtils.ts` - Utility functions for Cashfree integration
3. `components/payment/CashfreePayment.tsx` - React component for Cashfree payment
4. `app/api/payment/create-order/route.ts` - API route for creating a payment order
5. `app/api/payment/verify/route.ts` - API route for verifying a payment
6. `app/api/payment/callback/route.ts` - API route for handling payment callbacks
7. `app/payment/success/page.tsx` - Success page for successful payments
8. `app/payment/failure/page.tsx` - Failure page for failed payments
9. `app/payment/cashfree-test/page.tsx` - Test page for Cashfree payment flow

## Payment Flow

1. User selects a plan and initiates payment
2. Application creates an order with Cashfree via the server-side API
3. Cashfree returns an order token
4. Client-side JavaScript SDK uses the order token to initiate the payment
5. User completes the payment on Cashfree's payment page
6. Cashfree redirects the user back to the application with payment status
7. Application verifies the payment and updates the user's subscription status

## Testing

For testing the Cashfree integration, you can use the test page at `/payment/cashfree-test`. This page allows you to:

1. Select a plan (Standard or Pro)
2. Initialize a payment
3. Test the payment flow with Cashfree's sandbox environment
4. Simulate successful or failed payments

## Sandbox Testing

For testing in the sandbox environment, you can use the following test card details:

- Card Number: 4111 1111 1111 1111
- Expiry Date: Any future date
- CVV: Any 3-digit number
- Name: Any name

## Production Deployment

When deploying to production:

1. Update the environment variables to use production credentials
2. Set `CASHFREE_API_URL` to `https://api.cashfree.com/pg`
3. Ensure proper error handling and logging
4. Implement proper security measures for handling payment data

## Webhook Integration

Cashfree can send webhooks for payment events. To set up webhooks:

1. Configure a webhook URL in your Cashfree dashboard
2. Implement a webhook handler in your application
3. Verify the webhook signature to ensure it's from Cashfree

## Resources

- [Cashfree Documentation](https://docs.cashfree.com/docs/)
- [Cashfree Node.js SDK](https://github.com/cashfree/cashfree-pg-sdk-nodejs)
- [Cashfree JavaScript SDK](https://docs.cashfree.com/docs/web-integration-javascript)
