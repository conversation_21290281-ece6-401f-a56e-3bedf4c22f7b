'use client';

import { useEffect, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import SignUpForm from '@/components/auth/SignUpForm';
import { useAuth } from '@/context/AuthContext';

// Component that uses authentication
function SignUpContent() {
  const router = useRouter();
  const { user, isLoading } = useAuth();

  // Redirect authenticated users based on their project count
  useEffect(() => {
    const checkUserAndRedirect = async () => {
      if (!isLoading && user) {
        try {
          const response = await fetch('/api/projects');
          if (response.ok) {
            const data = await response.json();
            const userProjects = data.projects || [];

            // If user has no projects, redirect to onboarding
            if (userProjects.length === 0) {
              router.push('/onboarding');
            } else {
              // If user has projects, redirect to dashboard
              router.push('/dashboard');
            }
          } else {
            // If we can't fetch projects, default to dashboard
            router.push('/dashboard');
          }
        } catch (error) {
          console.error('Error checking user projects:', error);
          // If there's an error, default to dashboard
          router.push('/dashboard');
        }
      }
    };

    checkUserAndRedirect();
  }, [user, isLoading, router]);

  return (
    <div className="flex-grow flex items-center justify-center px-4 sm:px-6 lg:px-8 py-16 pt-24">
      <SignUpForm />
    </div>
  );
}

// Main page component with Suspense
export default function SignUpPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navbar />

      <Suspense fallback={
        <div className="flex-grow flex items-center justify-center px-4 sm:px-6 lg:px-8 py-16 pt-24">
          <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-6 w-1/2 mx-auto"></div>
              <div className="h-10 bg-gray-200 rounded mb-4"></div>
              <div className="h-10 bg-gray-200 rounded mb-4"></div>
              <div className="h-10 bg-gray-200 rounded mb-4"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      }>
        <SignUpContent />
      </Suspense>

      <Footer />
    </div>
  );
}
