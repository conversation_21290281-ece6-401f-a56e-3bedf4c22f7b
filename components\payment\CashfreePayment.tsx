'use client';

import { useState, useEffect } from 'react';
import { User } from '@/types';
import Script from 'next/script';
import { CASHFREE_JS_URL, PLAN_PRICES } from '@/utils/cashfreeConfig';

interface CashfreePaymentProps {
  user: User;
  onSuccess: () => void;
  onCancel: () => void;
  billingCycle?: 'monthly' | 'annual';
}

declare global {
  interface Window {
    Cashfree: (config: { mode: 'production' | 'sandbox' }) => {
      checkout: (options: {
        paymentSessionId: string;
        redirectTarget?: string;
      }) => Promise<{
        error?: any;
        redirect?: boolean;
        paymentDetails?: {
          paymentMessage: string;
          [key: string]: any;
        };
      }>;
    };
  }
}

const CashfreePayment = ({
  user,
  onSuccess,
  onCancel,
  billingCycle = 'monthly'
}: CashfreePaymentProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [orderToken, setOrderToken] = useState<string | null>(null);
  const [orderDetails, setOrderDetails] = useState<any>(null);

  // Calculate price based on plan and billing cycle
  const price = user.plan === 'standard'
    ? (billingCycle === 'monthly' ? PLAN_PRICES.standard.monthly : PLAN_PRICES.standard.annual)
    : user.plan === 'pro'
    ? (billingCycle === 'monthly' ? PLAN_PRICES.pro.monthly : PLAN_PRICES.pro.annual)
    : 0;

  useEffect(() => {
    const initializePayment = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Create an order on the server
        const response = await fetch('/api/payment/create-order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: user.id,
            plan: user.plan,
            amount: price,
            billingCycle
          }),
        });

        const data = await response.json();
        console.log('Create order response:', data);

        if (!data.success) {
          throw new Error(data.error || data.message || 'Failed to create payment order');
        }

        // Store the order token and details
        if (!data.orderToken) {
          console.error('No orderToken in response:', data);
          throw new Error('No payment token received from server');
        }

        setOrderToken(data.orderToken);
        setOrderDetails({
          orderId: data.orderId,
          cfOrderId: data.cfOrderId,
          orderStatus: data.orderStatus,
          orderExpiry: data.orderExpiry
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
        console.error('Payment initialization error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initializePayment();
  }, [user.id, user.plan, price, billingCycle]);

  // Handle real Cashfree payment
  const handleCashfreePayment = () => {
    if (!orderToken) {
      setError('No payment token available. Please try again.');
      return;
    }

    // Set loading state
    setIsLoading(true);

    // Function to initialize Cashfree
    const initializeCashfree = (retryCount = 0, maxRetries = 3) => {
      console.log(`Attempting to initialize Cashfree (attempt ${retryCount + 1}/${maxRetries + 1})`);

      // Check if Cashfree SDK is loaded
      if (typeof window !== 'undefined' && typeof window.Cashfree === 'function') {
        try {
          console.log('Initializing Cashfree payment with token:', orderToken);

          // Initialize Cashfree with mode
          const cashfree = window.Cashfree({
            mode: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
          });

          console.log('Cashfree instance created');

          // Define checkout options
          const checkoutOptions = {
            paymentSessionId: orderToken,
            redirectTarget: '_self', // Use '_self' for redirect checkout
          };

          // Open Cashfree checkout
          cashfree.checkout(checkoutOptions).then((result) => {
            console.log('Cashfree checkout result:', result);

            if (result.error) {
              // User closed the popup or there was an error
              console.log('Payment error or user closed popup:', result.error);
              setError('Payment was cancelled or failed. Please try again.');
              setIsLoading(false);
              onCancel();
            }

            if (result.redirect) {
              // Payment will be redirected
              console.log('Payment will be redirected');
              // The callback URL will handle success/failure
            }

            if (result.paymentDetails) {
              // Payment completed
              console.log('Payment completed:', result.paymentDetails);

              // Verify the payment
              verifyPayment({
                order_id: orderDetails?.orderId,
                transaction_id: result.paymentDetails.transaction_id || result.paymentDetails.referenceId,
                transaction_status: 'SUCCESS',
              });
            }
          }).catch((error) => {
            console.error('Cashfree checkout error:', error);
            setError('Payment failed. Please try again.');
            setIsLoading(false);
          });

        } catch (err) {
          console.error('Error initializing Cashfree payment:', err);

          // If we haven't reached max retries, try again after a delay
          if (retryCount < maxRetries) {
            console.log(`Retrying in ${(retryCount + 1) * 1000}ms...`);
            setTimeout(() => {
              initializeCashfree(retryCount + 1, maxRetries);
            }, (retryCount + 1) * 1000);
          } else {
            setError('Failed to initialize payment. Please refresh the page and try again.');
            setIsLoading(false);
          }
        }
      } else {
        console.error('Cashfree SDK not loaded');

        // If we haven't reached max retries, try again after a delay
        if (retryCount < maxRetries) {
          console.log(`Waiting for Cashfree SDK to load. Retrying in ${(retryCount + 1) * 1000}ms...`);
          setTimeout(() => {
            initializeCashfree(retryCount + 1, maxRetries);
          }, (retryCount + 1) * 1000);
        } else {
          setError('Cashfree SDK not loaded. Please refresh the page and try again.');
          setIsLoading(false);
        }
      }
    };

    // Start the initialization process
    initializeCashfree();
  };

  // No mock implementation for production

  const handleCancel = () => {
    console.log('Payment cancelled');
    onCancel();
  };

  const verifyPayment = async (paymentData: any) => {
    try {
      const response = await fetch('/api/payment/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentData),
      });

      const data = await response.json();

      if (data.success) {
        onSuccess();
      } else {
        setError(data.message || 'Payment verification failed');
      }
    } catch (err) {
      setError('Payment verification failed. Please contact support.');
      console.error('Payment verification error:', err);
    }
  };

  // Format price display based on billing cycle
  const formatPrice = () => {
    if (billingCycle === 'monthly') {
      return `$${price}/month`;
    } else {
      return `$${price}/year (${Math.round(price / 12)}/month)`;
    }
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <Script
        src={CASHFREE_JS_URL}
        strategy="afterInteractive"
        onLoad={() => {
          console.log('Cashfree script loaded successfully');
          // Check if Cashfree is available after loading
          if (typeof window !== 'undefined' && typeof window.Cashfree === 'function') {
            console.log('Cashfree SDK is available in window object');
          } else {
            console.error('Cashfree SDK not available in window object after script load');
            setError('Payment gateway not initialized. Please refresh the page and try again.');
          }
        }}
        onError={() => {
          console.error('Failed to load Cashfree script');
          setError('Failed to load payment gateway. Please refresh the page and try again.');
        }}
      />

      <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Complete Your Payment</h2>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <div className="mb-6">
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="font-medium text-gray-900">Order Summary</h3>
          <div className="mt-2 flex justify-between">
            <span className="text-gray-600">
              {user.plan.charAt(0).toUpperCase() + user.plan.slice(1)} Plan ({billingCycle})
            </span>
            <span className="font-medium">{formatPrice()}</span>
          </div>
          {orderDetails && (
            <div className="mt-2 pt-2 border-t border-gray-200 text-xs text-gray-500">
              Order ID: {orderDetails.orderId}
            </div>
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Payment button */}
          <button
            type="button"
            onClick={handleCashfreePayment}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Pay {formatPrice()}
          </button>
        </div>
      )}

      <div className="mt-6">
        <button
          type="button"
          onClick={handleCancel}
          disabled={isLoading}
          className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Cancel Payment
        </button>
      </div>
    </div>
  );
};

export default CashfreePayment;
