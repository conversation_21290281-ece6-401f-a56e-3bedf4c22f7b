/**
 * Cashfree Payment Gateway Configuration
 *
 * This file contains configuration settings for the Cashfree payment gateway.
 */

// Determine if we're in production based on environment
export const isProduction = process.env.NODE_ENV === 'production';

// Cashfree API version
export const CASHFREE_API_VERSION = '2023-08-01';

// Cashfree API URLs - Use environment-specific URL
export const CASHFREE_API_URL = process.env.CASHFREE_API_URL ||
  (isProduction ? 'https://api.cashfree.com/pg' : 'https://sandbox.cashfree.com/pg');

// Cashfree credentials
export const CASHFREE_APP_ID = process.env.CASHFREE_APP_ID || '';
export const CASHFREE_SECRET_KEY = process.env.CASHFREE_SECRET_KEY || '';

// Cashfree JS SDK URL
export const CASHFREE_JS_URL = process.env.NEXT_PUBLIC_CASHFREE_JS_URL || 'https://sdk.cashfree.com/js/v3/cashfree.js';

// Application base URL - Use environment-specific URL
export const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL ||
  (isProduction ? 'https://matrix.taqnik.in' : 'http://localhost:3000');

// Payment settings
export const PAYMENT_CURRENCY = 'INR';

// Plan prices (in INR)
export const PLAN_PRICES = {
  standard: {
    monthly: 1,
    annual: 15 * 12, // 15 per month, billed annually
  },
  pro: {
    monthly: 49,
    annual: 39 * 12, // 39 per month, billed annually
  },
};

// Payment modes to enable (leave empty for all)
export const PAYMENT_MODES = ''; // e.g., 'cc,dc,upi,nb'

// Order expiry time in minutes
export const ORDER_EXPIRY_MINUTES = 30;

// Webhook settings - Use environment-specific webhook URL
export const WEBHOOK_URL = `${BASE_URL}/api/payment/webhook`;

// Return URL settings
export const getReturnUrl = (orderId: string): string => {
  return `${BASE_URL}/api/payment/callback?orderId=${orderId}`;
};

// Logging settings
export const ENABLE_PAYMENT_LOGGING = true;

// Error messages
export const ERROR_MESSAGES = {
  ORDER_CREATION_FAILED: 'Failed to create payment order. Please try again.',
  PAYMENT_VERIFICATION_FAILED: 'Payment verification failed. Please contact support.',
  INVALID_SIGNATURE: 'Invalid payment signature. Please contact support.',
  USER_NOT_FOUND: 'User not found for this order. Please contact support.',
  GENERAL_ERROR: 'An error occurred while processing your payment. Please try again.',
};

// Success messages
export const SUCCESS_MESSAGES = {
  ORDER_CREATED: 'Order created successfully.',
  PAYMENT_VERIFIED: 'Payment verified successfully.',
  PAYMENT_COMPLETED: 'Payment completed successfully.',
};

// Payment status mapping
export const PAYMENT_STATUS = {
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  PENDING: 'PENDING',
};

// Database payment status mapping
export const DB_PAYMENT_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
};

// Logging function for payment-related events
export const logPaymentEvent = (event: string, data: any): void => {
  if (ENABLE_PAYMENT_LOGGING) {
    console.log(`[PAYMENT] [${new Date().toISOString()}] [${event}]`, JSON.stringify(data, null, 2));
  }
};
