'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import ProjectSelectionModal from '@/components/ProjectSelectionModal';
import ProjectDropdown from '@/components/ProjectDropdown';
import KeywordForm from '@/components/KeywordForm';
import { useAuth } from '@/context/AuthContext';
import { FormData, Matrix, Project } from '@/types';
import { generateMockWebhookData } from '@/utils/mockData';

// Dashboard Matrix content component that uses searchParams
function DashboardMatrixContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams ? searchParams.get('projectId') : null;

  // Log the projectId from URL for debugging
  console.log('Matrix page loaded with projectId from URL:', projectId);

  const { user, isLoading: authLoading } = useAuth();
  const [isProjectSelectionModalOpen, setIsProjectSelectionModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedMatrix, setGeneratedMatrix] = useState<Matrix | null>(null);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login-with-otp');
    }
  }, [user, authLoading, router]);

  // Handle project selection
  const handleProjectSelect = (project: Project) => {
    setSelectedProject(project);
  };

  // Handle form submission
  const handleFormSubmit = async (formData: FormData) => {
    if (!user || !selectedProject) return;

    setIsLoading(true);
    setError(null);

    try {
      // For demo purposes, we'll generate mock data
      const mockData = generateMockWebhookData(
        formData.mainKeyword,
        formData.location,
        formData.language
      );

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Create a new matrix with keyword-based ID
      const keywordSlug = formData.mainKeyword.toLowerCase().replace(/\s+/g, '-');
      const matrixId = `${keywordSlug}-${Date.now()}`;

      const newMatrix: Matrix = {
        id: matrixId,
        projectId: selectedProject.id, // Use the selected project ID
        userId: user.id,
        mainKeyword: formData.mainKeyword,
        filename: `${keywordSlug}-matrix.json`,
        location: formData.location,
        language: formData.language,
        keywordResearch: mockData.keywordResearch,
        contentMatrix: mockData.contentMatrix,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setGeneratedMatrix(newMatrix);

      // In a real application, you would save this to the database
      // For now, we'll just set it in state
      console.log('Generated matrix:', newMatrix);

      // Optionally, you could redirect to a page to view the matrix
      // router.push(`/matrix/${newMatrix.id}`);

    } catch (err) {
      console.error('Error generating matrix:', err);
      setError('Failed to generate matrix. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <ProjectSelectionModal
        isOpen={isProjectSelectionModalOpen}
        onClose={() => setIsProjectSelectionModalOpen(false)}
      />

      <div className="space-y-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Matrix Generator</h1>
          <p className="text-gray-600">
            Create and manage your SEO content matrices. Select a project and enter a keyword to get started.
          </p>
        </div>

        {/* Project Dropdown */}
        <ProjectDropdown
          onProjectSelect={handleProjectSelect}
          initialProjectId={projectId || undefined}
        />

        {/* Show Keyword Form only after project selection */}
        {selectedProject && (
          isLoading ? (
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
                <div className="h-64 bg-gray-200 rounded"></div>
              </div>
            </div>
          ) : (
            <KeywordForm onSubmit={handleFormSubmit} />
          )
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
            {error}
          </div>
        )}

        {generatedMatrix && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative">
            Matrix generated successfully for keyword: <strong>{generatedMatrix.mainKeyword}</strong> in project: <strong>{selectedProject?.name}</strong>
          </div>
        )}
      </div>
    </>
  );
}

// Main page component with Suspense
export default function DashboardMatrixPage() {
  return (
    <AppLayout>
      <Suspense fallback={
        <div className="space-y-8">
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      }>
        <DashboardMatrixContent />
      </Suspense>
    </AppLayout>
  );
}
