/* Collapsible menu animations */
.max-h-0 {
  max-height: 0;
}

.max-h-60 {
  max-height: 15rem;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-300 {
  transition-duration: 300ms;
}

.transform {
  transform: translateZ(0);
}

.rotate-180 {
  transform: rotate(180deg);
}

/* Smooth rotation for dropdown arrows */
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-200 {
  transition-duration: 200ms;
}

/* Tooltip styles for collapsed sidebar */
.sidebar-tooltip {
  position: relative;
}

.sidebar-tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 10px;
  padding: 4px 8px;
  background-color: rgba(79, 70, 229, 0.9);
  color: white;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 50;
  opacity: 0;
  animation: fadeIn 0.3s ease-in-out forwards;
}

.sidebar-tooltip:hover::before {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent rgba(79, 70, 229, 0.9) transparent transparent;
  z-index: 50;
  opacity: 0;
  animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
