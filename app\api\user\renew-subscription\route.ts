import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { createCashfreeOrder, generateOrderId } from '@/utils/cashfreeUtils';
import {
  PAYMENT_CURRENCY,
  getReturnUrl,
  WEBHOOK_URL,
  PAYMENT_MODES,
  PLAN_PRICES,
  DB_PAYMENT_STATUS
} from '@/utils/cashfreeConfig';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const { userId, plan, billingCycle = 'monthly' } = await request.json();

    // Validate input
    if (!userId || !plan) {
      return NextResponse.json(
        { success: false, message: 'User ID and plan are required' },
        { status: 400 }
      );
    }

    // Validate plan
    if (!['standard', 'pro'].includes(plan)) {
      return NextResponse.json(
        { success: false, message: 'Invalid plan selected' },
        { status: 400 }
      );
    }

    // Validate billing cycle
    if (!['monthly', 'annual'].includes(billingCycle)) {
      return NextResponse.json(
        { success: false, message: 'Invalid billing cycle' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Calculate amount based on plan and billing cycle
    let amount = 0;
    if (plan === 'standard') {
      amount = billingCycle === 'monthly' ? PLAN_PRICES.standard.monthly : PLAN_PRICES.standard.annual;
    } else if (plan === 'pro') {
      amount = billingCycle === 'monthly' ? PLAN_PRICES.pro.monthly : PLAN_PRICES.pro.annual;
    }

    // Generate a unique order ID
    const orderId = generateOrderId(userId);

    // Create order data for Cashfree
    const orderData = {
      order_id: orderId,
      order_amount: amount,
      order_currency: PAYMENT_CURRENCY,
      order_note: `Renewal for ${plan} plan (${billingCycle})`,
      customer_details: {
        customer_id: userId,
        customer_name: user.name,
        customer_email: user.email,
        customer_phone: user.phone || '9999999999' // Required by Cashfree
      },
      order_meta: {
        return_url: getReturnUrl(orderId),
        notify_url: WEBHOOK_URL || undefined,
        payment_methods: PAYMENT_MODES || undefined
      },
    };

    // Create order with Cashfree
    const cashfreeOrder = await createCashfreeOrder(orderData);

    // Update user's payment information
    user.paymentOrderId = orderId;
    user.paymentStatus = DB_PAYMENT_STATUS.PENDING;
    user.paymentPlan = plan;
    user.paymentAmount = amount;
    user.paymentBillingCycle = billingCycle;
    user.paymentCreatedAt = new Date();
    await user.save();

    // Return success response with order token
    return NextResponse.json({
      success: true,
      message: 'Renewal order created successfully',
      orderId: cashfreeOrder.order_id,
      orderToken: cashfreeOrder.order_token || cashfreeOrder.payment_session_id,
      cfOrderId: cashfreeOrder.cf_order_id,
      orderStatus: cashfreeOrder.order_status,
      orderExpiry: cashfreeOrder.order_expiry_time
    });
  } catch (error) {
    console.error('Error creating renewal order:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create renewal order' },
      { status: 500 }
    );
  }
}
