@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html, body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  width: 100%;
  max-width: 100%;
  position: relative;
  height: 100%;
}

/* Custom animations */
@layer utilities {
  .animate-in {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  /* Feature icon hover effects */
  .feature-icon-container {
    transition: all 0.3s ease;
  }

  .feature-icon-container:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
  }

  /* Fix for horizontal scrollbar */
  .container-fix {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Ensure all elements respect container boundaries */
  * {
    max-width: 100%;
  }

  /* Mobile menu scrolling */
  .mobile-menu-scroll {
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}
