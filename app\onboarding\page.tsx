'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import AppLayout from '@/components/AppLayout';
import KeywordForm from '@/components/KeywordForm';
import CreateProjectModal from '@/components/CreateProjectModal';

interface FormData {
  mainKeyword: string;
  location: string;
  language: string;
}

export default function OnboardingPage() {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState(false);
  const [keywordData, setKeywordData] = useState<FormData | null>(null);
  const [projects, setProjects] = useState([]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login-with-otp');
    }
  }, [user, authLoading, router]);

  // Check if user has projects and redirect if they do
  useEffect(() => {
    const checkUserProjects = async () => {
      if (!user) return;

      try {
        const response = await fetch('/api/projects');
        if (!response.ok) {
          throw new Error('Failed to fetch projects');
        }
        const data = await response.json();
        const userProjects = data.projects || [];
        
        setProjects(userProjects);
        
        // If user has projects, redirect to dashboard
        if (userProjects.length > 0) {
          router.push('/dashboard');
          return;
        }
      } catch (err) {
        console.error('Error checking user projects:', err);
        setError('Failed to load your data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      checkUserProjects();
    }
  }, [user, router]);

  const handleKeywordFormSubmit = (formData: FormData) => {
    // Store the keyword data and show project creation modal
    setKeywordData(formData);
    setIsCreateProjectModalOpen(true);
  };

  const handleCreateProject = async (name: string, description: string, website: string, niche: string) => {
    if (!user || !keywordData) return;

    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          website,
          niche,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create project');
      }

      const data = await response.json();
      const projectId = data.project._id;

      // Close the modal
      setIsCreateProjectModalOpen(false);

      // Redirect to the matrix page with both project ID and keyword data
      const queryParams = new URLSearchParams({
        projectId,
        mainKeyword: keywordData.mainKeyword,
        location: keywordData.location,
        language: keywordData.language,
        autoGenerate: 'true'
      });

      router.push(`/matrix?${queryParams.toString()}`);

    } catch (err) {
      console.error('Error creating project:', err);
      throw new Error('Failed to create project. Please try again.');
    }
  };

  // Show loading state
  if (authLoading || isLoading) {
    return (
      <AppLayout>
        <div className="space-y-8">
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Don't render if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <AppLayout>
      <CreateProjectModal
        isOpen={isCreateProjectModalOpen}
        onClose={() => setIsCreateProjectModalOpen(false)}
        onSubmit={handleCreateProject}
      />

      <div className="space-y-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2 text-center md:text-left">
            Welcome to SEO Content Matrix!
          </h1>
          <p className="text-gray-600 text-center md:text-left">
            Let's get started by creating your first content matrix. Enter your main keyword below to begin.
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
            {error}
          </div>
        )}

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Get Started with Your First Content Matrix
          </h2>
          <p className="text-gray-600 mb-6">
            Enter your main keyword information below. After submission, you'll be prompted to create a project to save your matrix data.
          </p>
          <KeywordForm onSubmit={handleKeywordFormSubmit} />
        </div>
      </div>
    </AppLayout>
  );
}
