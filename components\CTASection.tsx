import Link from 'next/link';

const CTASection = () => {
  return (
    <div className="bg-indigo-700">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
        <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
          <span className="block">Ready to boost your SEO strategy?</span>
          <span className="block text-indigo-200 text-base sm:text-xl mt-2">Start your free plan today.</span>
        </h2>
        <div className="mt-8 flex lg:mt-0 lg:flex-shrink-0">
          <div className="inline-flex rounded-md shadow w-full">
            <Link
              href="/signup"
              className="w-full font-bold inline-flex items-center justify-center px-5 py-3 border border-transparent text-base rounded-md text-indigo-600 bg-white hover:bg-indigo-50"
            >
              Try It Free
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CTASection;
