'use client';

import React, { useState, useEffect } from 'react';
import { Project } from '@/types';
import { useAuth } from '@/context/AuthContext';
import CreateProjectButton from './CreateProjectButton';

interface ProjectDropdownProps {
  onProjectSelect: (project: Project) => void;
  initialProjectId?: string;
}

const ProjectDropdown: React.FC<ProjectDropdownProps> = ({ onProjectSelect, initialProjectId }) => {
  const { user } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  // Fetch user's projects
  useEffect(() => {
    const fetchProjects = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        // Fetch projects from the API
        const response = await fetch('/api/projects', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // Include cookies for authentication
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch projects');
        }

        const data = await response.json();

        // Map the MongoDB documents to our Project type
        const fetchedProjects: Project[] = data.projects.map((project: any) => ({
          id: project._id,
          name: project.name,
          description: project.description || '',
          userId: project.userId,
          website: project.website || '',
          niche: project.niche,
          createdAt: project.createdAt,
          updatedAt: project.updatedAt,
        }));

        setProjects(fetchedProjects);

        // If initialProjectId is provided, select that project
        if (initialProjectId) {
          const initialProject = fetchedProjects.find(project => project.id === initialProjectId);
          if (initialProject) {
            setSelectedProject(initialProject);
            onProjectSelect(initialProject);
          }
        }
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError('Failed to load your projects. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, initialProjectId]);

  const handleSelectProject = (project: Project) => {
    setSelectedProject(project);
    setIsOpen(false);
    onProjectSelect(project);
  };

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-8">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">Select a Project</h2>

      {isLoading ? (
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      ) : projects.length === 0 ? (
        <div className="text-center py-4">
          <p className="text-gray-600 mb-4">You don't have any projects yet. Create your first project from the dashboard to get started.</p>
          <a
            href="/dashboard"
            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Go to Dashboard
          </a>
        </div>
      ) : (
        <div className="relative">
          <button
            type="button"
            className="relative w-full bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            onClick={() => setIsOpen(!isOpen)}
          >
            <span className="block truncate">
              {selectedProject ? selectedProject.name : 'Select a project'}
            </span>
            <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </span>
          </button>

          {isOpen && (
            <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
              {projects.map((project) => (
                <div
                  key={project.id}
                  className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-indigo-50"
                  onClick={() => handleSelectProject(project)}
                >
                  <span className="font-medium block truncate">{project.name}</span>
                  {project.description && (
                    <span className="text-gray-500 block truncate text-xs">{project.description}</span>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProjectDropdown;
