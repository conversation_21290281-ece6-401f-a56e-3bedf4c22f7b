import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { verifyPaymentStatus } from '@/utils/cashfreeSdk';
import {
  logPaymentEvent,
  PAYMENT_STATUS,
  DB_PAYMENT_STATUS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  isProduction,
  CASHFREE_SECRET_KEY
} from '@/utils/cashfreeConfig';

export const dynamic = 'force-dynamic';

/**
 * Webhook handler for Cashfree payment events
 *
 * This endpoint receives webhook notifications from Cashfree when payment events occur.
 * It verifies the webhook signature, processes the event, and updates the database accordingly.
 *
 * Webhook events include:
 * - ORDER_PAID: Payment successful
 * - PAYMENT_FAILED: Payment failed
 * - PAYMENT_USER_DROPPED: User abandoned the payment
 * - PAYMENT_EXPIRED: Payment session expired
 */
export async function POST(request: NextRequest) {
  try {
    // Get the webhook signature from the headers
    const cashfreeSignature = request.headers.get('x-webhook-signature');

    // Log the webhook request
    logPaymentEvent('WEBHOOK_RECEIVED', {
      url: request.url,
      hasSignature: !!cashfreeSignature
    });

    // Parse the webhook payload
    const webhookData = await request.json();

    // Log the webhook data (excluding sensitive information)
    logPaymentEvent('WEBHOOK_DATA', {
      event_type: webhookData.event_type,
      event_time: webhookData.event_time,
      order_id: webhookData.data?.order?.order_id
    });

    // Always verify the webhook signature
    if (cashfreeSignature) {
      const crypto = require('crypto');
      const payload = JSON.stringify(webhookData);
      const computedSignature = crypto
        .createHmac('sha256', CASHFREE_SECRET_KEY)
        .update(payload)
        .digest('base64');

      if (computedSignature !== cashfreeSignature) {
        logPaymentEvent('WEBHOOK_INVALID_SIGNATURE', {
          expected: computedSignature ? computedSignature.substring(0, 8) + '...' : '',
          received: cashfreeSignature ? cashfreeSignature.substring(0, 8) + '...' : ''
        });

        return NextResponse.json(
          { success: false, message: ERROR_MESSAGES.INVALID_SIGNATURE },
          { status: 401 }
        );
      }
    }

    // Extract the order ID from the webhook data
    const orderId = webhookData.data?.order?.order_id;

    if (!orderId) {
      logPaymentEvent('WEBHOOK_MISSING_ORDER_ID', { webhookData });
      return NextResponse.json(
        { success: false, message: 'Missing order ID in webhook data' },
        { status: 400 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Find the user with this order ID
    const user = await User.findOne({ paymentOrderId: orderId });

    if (!user) {
      logPaymentEvent('WEBHOOK_USER_NOT_FOUND', { orderId });
      return NextResponse.json(
        { success: false, message: ERROR_MESSAGES.USER_NOT_FOUND },
        { status: 404 }
      );
    }

    // Process the webhook based on the event type
    switch (webhookData.event_type) {
      case 'ORDER_PAID':
        // Payment successful
        const paymentId = webhookData.data?.payment?.cf_payment_id || webhookData.data?.payment?.payment_id;

        // Always verify the payment status with the API
        if (paymentId) {
          try {
            const paymentVerification = await verifyPaymentStatus(orderId, paymentId);

            if (!paymentVerification.success) {
              logPaymentEvent('WEBHOOK_PAYMENT_VERIFICATION_FAILED', {
                orderId,
                paymentId,
                status: paymentVerification.status
              });

              // Continue processing but log the discrepancy
            }
          } catch (verifyError) {
            logPaymentEvent('WEBHOOK_PAYMENT_VERIFICATION_ERROR', {
              orderId,
              paymentId,
              error: verifyError instanceof Error ? verifyError.message : 'Unknown error'
            });

            // Continue processing despite the error
          }
        }

        // Update the user's payment status
        user.paymentStatus = DB_PAYMENT_STATUS.COMPLETED;
        user.paymentReference = paymentId || 'webhook_reference';
        user.paymentMode = webhookData.data?.payment?.payment_method?.payment_method_type || 'unknown';
        user.paymentTime = new Date(webhookData.event_time || Date.now());
        await user.save();

        logPaymentEvent('WEBHOOK_PAYMENT_SUCCESS_DB_UPDATED', {
          orderId,
          userId: user._id,
          plan: user.paymentPlan || user.plan
        });

        break;

      case 'PAYMENT_FAILED':
        // Payment failed
        user.paymentStatus = DB_PAYMENT_STATUS.FAILED;
        user.paymentReference = webhookData.data?.payment?.cf_payment_id || 'webhook_reference';
        user.paymentFailureReason = webhookData.data?.payment?.error_details?.error_description || 'Payment failed';
        await user.save();

        logPaymentEvent('WEBHOOK_PAYMENT_FAILED_DB_UPDATED', {
          orderId,
          userId: user._id,
          reason: user.paymentFailureReason
        });

        break;

      case 'PAYMENT_USER_DROPPED':
      case 'PAYMENT_EXPIRED':
        // User abandoned the payment or payment expired
        user.paymentStatus = DB_PAYMENT_STATUS.FAILED;
        user.paymentFailureReason = webhookData.event_type === 'PAYMENT_USER_DROPPED'
          ? 'Payment abandoned by user'
          : 'Payment session expired';
        await user.save();

        logPaymentEvent('WEBHOOK_PAYMENT_ABANDONED_DB_UPDATED', {
          orderId,
          userId: user._id,
          event: webhookData.event_type
        });

        break;

      default:
        // Log unknown event types but don't update the database
        logPaymentEvent('WEBHOOK_UNKNOWN_EVENT_TYPE', {
          orderId,
          eventType: webhookData.event_type
        });
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully'
    });
  } catch (error) {
    logPaymentEvent('WEBHOOK_ERROR', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return NextResponse.json(
      { success: false, message: 'An error occurred while processing webhook' },
      { status: 500 }
    );
  }
}
