import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  try {
    const { userId, plan, amount } = await request.json();

    // Validate input
    if (!userId || !plan) {
      return NextResponse.json(
        { success: false, message: 'User ID and plan are required' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // In a real application, you would integrate with a payment provider like Stripe
    // For this demo, we'll simulate a successful payment

    // Update user's payment status
    user.paymentStatus = 'completed';
    await user.save();

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Payment processed successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        plan: user.plan,
        paymentStatus: user.paymentStatus,
        createdAt: user.createdAt,
      },
    });
  } catch (error) {
    console.error('Payment processing error:', error);
    return NextResponse.json(
      { success: false, message: 'An error occurred while processing payment' },
      { status: 500 }
    );
  }
}
