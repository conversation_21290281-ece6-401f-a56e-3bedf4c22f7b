export interface FormData {
  mainKeyword: string;
  location: string;
  language: string;
}

export interface KeywordResearchItem {
  mainKeyword: string;
  type: string;
  keyword: string;
  msv: number;
  searchIntent: string;
  kwDifficulty: number;
  competition: number;
  cpc: number;
  answer: string;
  timestamp: string;
}

export interface ContentMatrixItem {
  mainKeyword: string;
  cluster: string;
  contentType: string;
  focus: string;
  category: string;
  url: string;
  searchVol: number;
  keywords: string;
  status: string;
}

export interface WebhookData {
  mainKeyword: string;
  location: string;
  language: string;
  limit: number;
  date: string;
  timestamp: string;
  keywordResearch: KeywordResearchItem[];
  contentMatrix: ContentMatrixItem[];
}

export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  plan: PlanType;
  paymentStatus?: 'pending' | 'completed' | 'failed';
  paymentOrderId?: string;
  paymentReference?: string;
  paymentMode?: string;
  paymentTime?: string;
  paymentAmount?: number;
  paymentBillingCycle?: 'monthly' | 'annual';
  paymentExpiryDate?: string;
  createdAt: string;
}

export type PlanType = 'free' | 'standard' | 'pro';

export interface SignUpFormData {
  name: string;
  email: string;
  plan: PlanType;
}

export interface OTPFormData {
  email: string;
  otp: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: User;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  userId: string;
  website?: string;
  niche: string;
  createdAt: string;
  updatedAt: string;
}

export interface Matrix {
  id: string;
  projectId: string;
  userId: string;
  mainKeyword: string;
  filename: string;
  location: string;
  language: string;
  keywordResearch: KeywordResearchItem[];
  contentMatrix: ContentMatrixItem[];
  createdAt: string;
  updatedAt: string;
}
