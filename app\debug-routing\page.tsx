'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/AppLayout';

export default function DebugRoutingPage() {
  const router = useRouter();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addLog = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testRouting = () => {
    addLog('🚀 Starting routing test...');
    
    // Test 1: Simple routing
    const testUrl = '/project/test-project/matrix/test-matrix-123';
    addLog(`📍 Testing route: ${testUrl}`);
    
    try {
      router.push(testUrl);
      addLog('✅ Router.push executed successfully');
    } catch (error) {
      addLog(`❌ Router.push failed: ${error}`);
    }
  };

  const testFormSubmission = async () => {
    addLog('🧪 Testing form submission flow...');
    
    const mockFormData = {
      mainKeyword: 'ai tools for productivity',
      location: 'United States',
      language: 'English'
    };
    
    addLog(`📝 Mock form data: ${JSON.stringify(mockFormData)}`);
    
    try {
      // Simulate the API call
      addLog('📤 Simulating API call...');
      
      const response = await fetch('/api/matrices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId: 'test-project-id',
          ...mockFormData,
          keywordResearch: [],
          contentMatrix: [],
        }),
      });
      
      addLog(`📥 API Response status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        addLog(`✅ API Success: ${JSON.stringify(data)}`);
        
        // Test routing after successful API call
        const redirectUrl = `/project/test-project/matrix/${data.matrix?._id || 'test-id'}`;
        addLog(`🔄 Would redirect to: ${redirectUrl}`);
        
        setTimeout(() => {
          router.push(redirectUrl);
        }, 2000);
        
      } else {
        const errorData = await response.json();
        addLog(`❌ API Error: ${JSON.stringify(errorData)}`);
      }
      
    } catch (error) {
      addLog(`💥 Error: ${error}`);
    }
  };

  const clearLogs = () => {
    setTestResults([]);
  };

  return (
    <AppLayout>
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">🐛 Debug Routing</h1>
        
        <div className="bg-white shadow-md rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Test Controls</h2>
          
          <div className="flex flex-wrap gap-4 mb-6">
            <button
              onClick={testRouting}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Test Simple Routing
            </button>
            
            <button
              onClick={testFormSubmission}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Test Form Submission Flow
            </button>
            
            <button
              onClick={clearLogs}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Clear Logs
            </button>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-gray-800 mb-2">Test Results:</h3>
            <div className="space-y-1 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500 italic">No tests run yet...</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono bg-white p-2 rounded border">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-yellow-800 mb-2">🔍 Debug Information</h3>
          <div className="text-sm text-yellow-700 space-y-1">
            <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
            <p><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent : 'N/A'}</p>
            <p><strong>Router Ready:</strong> {router ? '✅ Yes' : '❌ No'}</p>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
