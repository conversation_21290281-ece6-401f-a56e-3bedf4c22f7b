'use client';

import React from 'react';
import Link from 'next/link';
import { Matrix } from '@/types';

interface MatrixCardProps {
  matrix: Matrix;
  projectId: string;
  projectName?: string;
}

const MatrixCard: React.FC<MatrixCardProps> = ({ matrix, projectId, projectName }) => {
  // Create a slug from the project name if provided, otherwise use the projectId
  const projectSlug = projectName
    ? projectName.toLowerCase().replace(/\s+/g, '-')
    : projectId;
  return (
    <Link href={`/project/${projectSlug}/matrix/${matrix.id}`} className="block">
      <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
        <div className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">{matrix.mainKeyword}</h3>
              <p className="text-xs text-gray-500 mb-2">
                {matrix.filename}
              </p>
              <div className="flex items-center text-xs text-gray-500">
                <span className="inline-block mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  {matrix.location}
                </span>
                <span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.20l.8 9.1a1 1 0 01-1 1.1H4a1 1 0 01-1-1.1L3.8 6H2a1 1 0 110-2h3V3a1 1 0 011-1h1zm0 2v2H5V4h2zm7 6a2 2 0 11-4 0 2 2 0 014 0z" clipRule="evenodd" />
                  </svg>
                  {matrix.language}
                </span>
              </div>
            </div>
            <div className="bg-indigo-100 rounded-full p-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          <div className="mt-4 flex justify-between items-center">
            <div className="text-xs text-gray-500">
              Created: {new Date(matrix.createdAt).toLocaleDateString()}
            </div>
            <div className="flex space-x-2">
              <div className="text-xs bg-green-50 text-green-700 px-2 py-1 rounded-full">
                {matrix.keywordResearch.length} Keywords
              </div>
              <div className="text-xs bg-indigo-50 text-indigo-700 px-2 py-1 rounded-full">
                {matrix.contentMatrix.length} Content Items
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default MatrixCard;
