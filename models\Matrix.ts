import mongoose, { Schema, Document } from 'mongoose';
import { KeywordResearchItem, ContentMatrixItem } from '@/types';

export interface IMatrix extends Document {
  projectId: mongoose.Schema.Types.ObjectId;
  userId: mongoose.Schema.Types.ObjectId;
  mainKeyword: string;
  filename: string;
  location: string;
  language: string;
  keywordResearch: KeywordResearchItem[];
  contentMatrix: ContentMatrixItem[];
  createdAt: Date;
  updatedAt: Date;
}

const MatrixSchema: Schema = new Schema(
  {
    projectId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Project',
      required: [true, 'Project ID is required'],
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    mainKeyword: {
      type: String,
      required: [true, 'Main keyword is required'],
    },
    filename: {
      type: String,
      required: [true, 'Filename is required'],
    },
    location: {
      type: String,
      default: 'United States',
    },
    language: {
      type: String,
      default: 'English',
    },
    keywordResearch: {
      type: Array,
      default: [],
    },
    contentMatrix: {
      type: Array,
      default: [],
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.models.Matrix || mongoose.model<IMatrix>('Matrix', MatrixSchema);
