import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/context/AuthContext";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "MATRIX | Boost Your Content Strategy",
  description: "Generate SEO-optimized content matrices from keywords. Plan, research, and optimize your content strategy for better search rankings.",
  keywords: "SEO, content matrix, keyword research, content planning, SEO optimization, content strategy",
  authors: [{ name: "MATRIX Team" }],
  openGraph: {
    title: "MATRIX | Boost Your Content Strategy",
    description: "Generate SEO-optimized content matrices from keywords. Plan, research, and optimize your content strategy for better search rankings.",
    url: "https://MATRIX.com",
    siteName: "MATRIX",
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "MATRIX | Boost Your Content Strategy",
    description: "Generate SEO-optimized content matrices from keywords. Plan, research, and optimize your content strategy for better search rankings.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
      </head>
      <body className={`${inter.variable} antialiased overflow-x-hidden`}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
