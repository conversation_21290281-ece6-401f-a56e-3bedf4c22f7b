'use client';

import { useState } from 'react';

const FAQSection = () => {
  const faqs = [
    {
      question: 'What is Matrix?',
      answer:
        'Matrix is a tool that helps you create comprehensive content plans based on keyword research and provide ready-made content maps—all without requiring any technical SEO knowledge.',
    },
    {
      question: 'Do I need SEO experience to use this tool?',
      answer:
        'Not at all! We\'ve stripped away the complexity. Just enter your keywords and get structured, SEO-smart content ideas—no technical setup or SEO knowledge required. Our tool is designed to be intuitive for beginners while still providing powerful features for experienced users.',
    },
    {
      question: 'How does the keyword clustering work?',
      answer:
        'Our one-click content clustering automatically groups your keywords by search intent and topical relevance. This helps you plan pillar and supporting articles that truly match what people are searching for, without any manual sorting or spreadsheet work required.',
    },
    {
      question: 'Can I target specific locations or languages?',
      answer:
        'Yes! Our Local Topics feature lets you focus your content by region or language. We surface keyword clusters that align with your target markets, making it easy to create location-specific content strategies without manual sorting.',
    },
    {
      question: 'What\'s included in the free plan?',
      answer:
        'The Free plan includes 10 keyword searches per month, basic keyword suggestions (volume, CPC, difficulty), one complete content cluster, and basic features. It\'s perfect for beginners and casual users who want to try out the platform before committing to a paid plan.',
    },
    {
      question: 'What\'s the difference between Standard and Pro plans?',
      answer:
        'The Standard plan ($20/month or $15/month billed annually) includes 150 keyword searches/month, 25 saved projects, and the ability to generate up to 3000+ optimized content titles monthly. The Pro plan ($50/month or $35/month billed annually) offers unlimited keyword searches, unlimited content titles, unlimited projects, bulk export capabilities, and priority support.',
    },
    {
      question: 'Can I export the data to other tools?',
      answer:
        'Yes! The Standard plan allows export to CSV, while the Pro plan includes bulk export capabilities. This makes it easy to integrate with your existing content management systems or SEO tools.',
    },
    {
      question: 'How do the content maps work?',
      answer:
        'Our ready-made content maps provide clean, actionable content structures including pillar topics, supporting content, and subtopics—all automatically mapped from your keyword list. This gives you a clear roadmap for creating comprehensive content that covers topics thoroughly and ranks for multiple keywords.',
    },
  ];

  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div id="faq" className="py-12 bg-gray-50 scroll-mt-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div>
          {/* Uppercase label - centered on all devices */}
          <h2 className="text-base text-indigo-600 font-semibold tracking-wide uppercase text-center">FAQ</h2>
          {/* Main heading - centered on all devices */}
          <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl text-center">
            Questions about our content matrix tool?
          </p>
          {/* Subheading - left-aligned on mobile, centered on lg screens */}
          <p className="mt-4 max-w-2xl text-base sm:text-xl text-gray-500 text-left lg:text-center lg:mx-auto">
            Get answers to common questions about features, pricing, and how our tool simplifies your SEO content strategy.
          </p>
        </div>

        <div className="mt-12 max-w-3xl mx-auto">
          <dl className="space-y-6 divide-y divide-gray-200">
            {faqs.map((faq, index) => (
              <div key={index} className="pt-6">
                <dt className="text-lg">
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="text-left w-full flex justify-between items-start text-gray-900 focus:outline-none"
                  >
                    <span className="font-medium text-gray-900">{faq.question}</span>
                    <span className="ml-6 h-7 flex items-center">
                      <svg
                        className={`${
                          openIndex === index ? '-rotate-180' : 'rotate-0'
                        } h-6 w-6 transform transition-transform duration-200 ease-in-out text-indigo-600`}
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        aria-hidden="true"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </span>
                  </button>
                </dt>
                <dd
                  className={`${
                    openIndex === index ? 'block' : 'hidden'
                  } mt-2 pr-12 transition-all duration-200 ease-in-out`}
                >
                  <p className="text-base text-gray-500">{faq.answer}</p>
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </div>
  );
};

export default FAQSection;
