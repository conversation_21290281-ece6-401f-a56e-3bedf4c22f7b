import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { saveOTP } from '../otp-actions';
import { sendOTPToWebhook } from '@/utils/webhookUtils';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // Validate input
    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email is required' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Check if user exists
    let user = await User.findOne({ email });

    // If user doesn't exist, return a response to redirect to signup
    if (!user) {
      return NextResponse.json({
        success: false,
        redirect: 'signup',
        email,
        message: 'User not registered. Please sign up first.'
      });
    }

    // Save OTP to database and get the generated OTP
    const otp = await saveOTP(email);

    try {
      // Log for debugging
      console.log(`Sending OTP for ${email}: ${otp} via webhook`);

      // Use the webhook utility function to send OTP data
      const webhookResponse = await sendOTPToWebhook({
        name: user.name,
        email,
        otp,
        route: 'send-otp',
        timestamp: Date.now(),
      });

      console.log(`Webhook response:`, webhookResponse);
      console.log(`Webhook response type:`, typeof webhookResponse);

      // For debugging
      if (typeof webhookResponse === 'string') {
        console.log(`Webhook response as string:`, webhookResponse);
        try {
          const parsedResponse = JSON.parse(webhookResponse);
          console.log(`Parsed webhook response:`, parsedResponse);
        } catch (e) {
          console.log(`Not a JSON string`);
        }
      }

      // Check if the webhook response indicates successful sending
      const isSent = webhookResponse?.isSent === true ||
                     webhookResponse?.success === true ||
                     webhookResponse === true ||
                     webhookResponse === "true" ||
                     (typeof webhookResponse === 'string' && webhookResponse.toLowerCase() === "true") ||
                     (webhookResponse?.message === "Workflow was started");

      if (isSent) {
        // Return success response
        return NextResponse.json({
          success: true,
          message: 'OTP sent successfully',
          email,
        });
      } else {
        // Return response indicating OTP was not sent
        return NextResponse.json({
          success: false,
          message: 'OTP generated but delivery failed. Please try again.',
          email,
          webhookResponse,
        }, { status: 400 });
      }
    } catch (webhookError) {
      console.error('Error sending OTP to webhook:', webhookError);
      console.error('Error details:', webhookError instanceof Error ? webhookError.message : String(webhookError));

      // Try direct webhook call as a fallback
      try {
        console.log(`Attempting direct webhook call as fallback for ${email}`);

        // Get the webhook URL
        const webhookUrl = process.env.NEXT_PUBLIC_OTP_WEBHOOK_URL;

        // If webhook URL is not configured, return error
        if (!webhookUrl) {
          throw new Error('Webhook URL is not configured. Please set NEXT_PUBLIC_OTP_WEBHOOK_URL environment variable.');
        }

        // Make direct call
        const response = await fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Debug-Info': 'Fallback direct webhook call',
          },
          body: JSON.stringify({
            name: user.name,
            email,
            otp,
            route: 'send-otp',
            timestamp: Date.now(),
            debug: {
              source: 'matrix-app-fallback',
              error: webhookError instanceof Error ? webhookError.message : String(webhookError),
            },
          }),
        });

        if (response.ok) {
          console.log('Fallback webhook call succeeded');

          // Try to parse the response
          const responseText = await response.text();
          console.log(`Fallback raw response text:`, responseText);
          console.log(`Fallback response text type:`, typeof responseText);

          let responseData;
          try {
            responseData = JSON.parse(responseText);
            console.log(`Fallback parsed response:`, responseData);
          } catch (e) {
            console.log(`Fallback response is not JSON, using as-is`);
            // If the response is just "true" as a string, use it directly
            if (responseText === "true") {
              responseData = true;
            } else {
              responseData = { message: responseText };
            }
          }

          // Check if the webhook response indicates successful sending
          const isSent = responseData?.webhookResponse === true ||
                         responseData?.isSent === true ||
                         responseData?.success === true ||
                         responseData === true ||
                         responseText === "true" ||
                         (typeof responseText === 'string' && responseText.toLowerCase() === "true") ||
                         (responseData?.message === "Workflow was started");

          if (isSent) {
            return NextResponse.json({
              success: true,
              message: 'OTP sent successfully (via fallback)',
              email,
            });
          } else {
            return NextResponse.json({
              success: false,
              message: 'OTP generated but delivery failed. Please try again.',
              email,
              webhookResponse: responseData,
            }, { status: 400 });
          }
        } else {
          throw new Error(`Fallback webhook call failed with status ${response.status}`);
        }
      } catch (fallbackError) {
        console.error('Fallback webhook call also failed:', fallbackError);

        // Return failure response since both methods failed
        return NextResponse.json({
          success: false,
          message: 'OTP generated but delivery failed. Please try again.',
          email,
          error: 'All webhook delivery methods failed',
        }, { status: 400 });
      }
    }
  } catch (error) {
    console.error('Error sending OTP:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to send OTP' },
      { status: 500 }
    );
  }
}
