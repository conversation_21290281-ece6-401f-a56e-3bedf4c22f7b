{"name": "matrix", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@cashfreepayments/cashfree-js": "^1.0.5", "@types/nodemailer": "^6.4.17", "autoprefixer": "^10.4.17", "cashfree-pg": "^5.0.8", "dotenv": "^16.5.0", "googleapis": "^148.0.0", "mongodb": "^6.16.0", "mongoose": "^8.14.3", "next": "^14.1.0", "nodemailer": "^7.0.3", "postcss": "^8.4.35", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0"}}