import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';
import { saveOTP } from '../otp-actions';
import { sendOTPToWebhook } from '@/utils/webhookUtils';

// This route is now just a redirect to the OTP flow
export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // Validate input
    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email is required' },
        { status: 400 }
      );
    }

    // Connect to database
    await dbConnect();

    // Check if user exists and get their name
    let user = await User.findOne({ email });

    // Default name if user doesn't exist
    const name = user ? user.name : email.split('@')[0];

    // Generate and save OTP
    const otp = await saveOTP(email);

    try {
      // Send OTP via webhook
      const webhookResponse = await sendOTPToWebhook({
        name,
        email,
        otp,
        route: 'signin',
        timestamp: Date.now(),
      });

      // Check if the webhook response indicates successful sending
      const isSent = webhookResponse?.isSent === true ||
                     webhookResponse?.success === true ||
                     webhookResponse === true ||
                     webhookResponse === "true" ||
                     (typeof webhookResponse === 'string' && webhookResponse.toLowerCase() === "true") ||
                     (webhookResponse?.message === "Workflow was started") ||
                     (webhookResponse?.message === "OTP would be sent in production environment");

      if (isSent) {
        // Return success response
        return NextResponse.json({
          success: true,
          message: 'OTP sent successfully',
        });
      } else {
        // Log the webhook response for debugging
        console.warn('Webhook response indicated OTP was not sent:', JSON.stringify(webhookResponse));

        // In production, we'll still return success to avoid blocking users
        if (process.env.NODE_ENV === 'production') {
          console.log('In production environment, returning success response despite webhook failure');
          return NextResponse.json({
            success: true,
            message: 'OTP sent successfully',
          });
        } else {
          // In development, return the actual error
          return NextResponse.json({
            success: false,
            message: 'OTP generation successful but delivery failed. Please try again.',
            webhookResponse,
          }, { status: 400 });
        }
      }
    } catch (webhookError) {
      console.error('Error sending OTP via webhook:', webhookError);

      // In production, we'll still return success to avoid blocking users
      if (process.env.NODE_ENV === 'production') {
        console.log('In production environment, returning success response despite webhook error');
        return NextResponse.json({
          success: true,
          message: 'OTP sent successfully',
        });
      } else {
        return NextResponse.json({
          success: false,
          message: 'Failed to send OTP. Please try again.',
        }, { status: 500 });
      }
    }
  } catch (error) {
    console.error('Error in signin route:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process request' },
      { status: 500 }
    );
  }
}
