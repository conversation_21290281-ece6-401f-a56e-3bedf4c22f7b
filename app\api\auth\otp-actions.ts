'use server';

import dbConnect from '@/lib/mongodb';
import OTP from '@/models/OTP';
import { generateOTP } from '@/utils/otpUtils';

// Save OTP to database
export async function saveOTP(email: string): Promise<string> {
  await dbConnect();
  
  // Generate OTP
  const otp = generateOTP();
  
  // Delete any existing OTPs for this email
  await OTP.deleteMany({ email });

  // Create expiration date (10 minutes from now)
  const expiresAt = new Date();
  expiresAt.setMinutes(expiresAt.getMinutes() + 10);

  // Create new OTP document
  await OTP.create({
    email,
    otp,
    expiresAt,
  });
  
  return otp;
}

// Verify OTP
export async function verifyOTP(email: string, otp: string): Promise<boolean> {
  await dbConnect();
  
  const otpRecord = await OTP.findOne({
    email,
    otp,
    expiresAt: { $gt: new Date() },
  });

  if (!otpRecord) {
    return false;
  }

  // Delete the OTP after successful verification
  await OTP.deleteOne({ _id: otpRecord._id });

  return true;
}
