'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@/types';

interface PaymentFormProps {
  user: User;
  onSuccess: () => void;
  onCancel: () => void;
}

const PaymentForm = ({ user, onSuccess, onCancel }: PaymentFormProps) => {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cardDetails, setCardDetails] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    nameOnCard: '',
  });

  // Calculate price based on plan
  const price = user.plan === 'standard' ? 20 : user.plan === 'pro' ? 49 : 0;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCardDetails(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setIsProcessing(true);

    try {
      // In a real application, you would integrate with a payment provider like Stripe
      // For this demo, we'll simulate a payment process
      
      // Validate card details
      if (cardDetails.cardNumber.replace(/\s/g, '').length !== 16) {
        throw new Error('Invalid card number');
      }

      if (cardDetails.cvv.length !== 3) {
        throw new Error('Invalid CVV');
      }

      // Simulate API call to process payment
      const response = await fetch('/api/payment/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          plan: user.plan,
          amount: price,
        }),
      });

      const data = await response.json();

      if (data.success) {
        onSuccess();
      } else {
        setError(data.message || 'Payment failed. Please try again.');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      console.error('Payment error:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Complete Your Payment</h2>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <div className="mb-6">
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="font-medium text-gray-900">Order Summary</h3>
          <div className="mt-2 flex justify-between">
            <span className="text-gray-600">{user.plan.charAt(0).toUpperCase() + user.plan.slice(1)} Plan</span>
            <span className="font-medium">${price}/month</span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="nameOnCard" className="block text-sm font-medium text-gray-700">
            Name on Card
          </label>
          <input
            id="nameOnCard"
            name="nameOnCard"
            type="text"
            required
            value={cardDetails.nameOnCard}
            onChange={handleChange}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          />
        </div>

        <div>
          <label htmlFor="cardNumber" className="block text-sm font-medium text-gray-700">
            Card Number
          </label>
          <input
            id="cardNumber"
            name="cardNumber"
            type="text"
            required
            maxLength={19}
            value={cardDetails.cardNumber}
            onChange={(e) => {
              // Format card number with spaces
              const value = e.target.value.replace(/\s/g, '');
              const formattedValue = value.replace(/(\d{4})/g, '$1 ').trim();
              setCardDetails(prev => ({ ...prev, cardNumber: formattedValue }));
            }}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="1234 5678 9012 3456"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700">
              Expiry Date
            </label>
            <input
              id="expiryDate"
              name="expiryDate"
              type="text"
              required
              maxLength={5}
              value={cardDetails.expiryDate}
              onChange={(e) => {
                // Format expiry date as MM/YY
                const value = e.target.value.replace(/\//g, '');
                if (value.length <= 2) {
                  setCardDetails(prev => ({ ...prev, expiryDate: value }));
                } else {
                  const formattedValue = `${value.slice(0, 2)}/${value.slice(2)}`;
                  setCardDetails(prev => ({ ...prev, expiryDate: formattedValue }));
                }
              }}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="MM/YY"
            />
          </div>
          <div>
            <label htmlFor="cvv" className="block text-sm font-medium text-gray-700">
              CVV
            </label>
            <input
              id="cvv"
              name="cvv"
              type="text"
              required
              maxLength={3}
              value={cardDetails.cvv}
              onChange={(e) => {
                // Only allow numbers
                const value = e.target.value.replace(/\D/g, '');
                setCardDetails(prev => ({ ...prev, cvv: value }));
              }}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="123"
            />
          </div>
        </div>

        <div className="flex space-x-4">
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isProcessing}
            className="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isProcessing ? 'Processing...' : `Pay $${price}`}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PaymentForm;
