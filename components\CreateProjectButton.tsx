'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import CreateProjectModal from './CreateProjectModal';

interface CreateProjectButtonProps {
  buttonText?: string;
  redirectToMatrix?: boolean;
}

const CreateProjectButton: React.FC<CreateProjectButtonProps> = ({
  buttonText = 'Create Project',
  redirectToMatrix = false
}) => {
  const router = useRouter();
  const { user } = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleCreateProject = async (name: string, description: string, website: string, niche: string) => {
    if (!user) return;

    try {
      // Send project data to the API
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify({
          name,
          description,
          website,
          niche,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create project');
      }

      const data = await response.json();

      // Close the modal
      setIsModalOpen(false);

      // Redirect based on the redirectToMatrix prop
      if (redirectToMatrix) {
        // Get the project ID from the response
        const projectId = data.project._id;
        console.log('Created project with ID:', projectId);

        // Redirect to the matrix page with the new project ID as a query parameter
        const redirectUrl = `/matrix?projectId=${projectId}`;
        console.log('Redirecting to:', redirectUrl);
        router.push(redirectUrl);
      } else {
        // Redirect to the project page using the slug format
        const projectSlug = name.toLowerCase().replace(/\s+/g, '-');
        router.push(`/project/${projectSlug}`);
      }
    } catch (err) {
      console.error('Error creating project:', err);
    }
  };

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        {buttonText}
      </button>

      <CreateProjectModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleCreateProject}
      />
    </>
  );
};

export default CreateProjectButton;
